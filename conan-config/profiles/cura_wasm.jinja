include(cura.jinja)

[tool_requires]
emsdk/[>=3.1.73]@ultimaker/stable
nodejs/[>=20.16.0]@ultimaker/stable

[settings]
os=Emscripten
arch=wasm

[conf]
tools.build:skip_test=True
tools.cmake.cmake_layout:build_folder_vars=['settings.os']
user.curator:printers=['ultimaker_sketch','ultimaker_sketch_large','ultimaker_sketch_sprint','ultimaker_method','ultimaker_methodx','ultimaker_methodxl', 'ultimaker_s3']

[options]
curaengine/*:enable_plugins=False
curaengine/*:enable_arcus=False
curator/*:with_cura_resources=True
curator/*:disable_logging=True
dulcificum/*:with_python_bindings=False
libzip/*:crypto=False
