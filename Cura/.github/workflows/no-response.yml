name: No Response

# Both `issue_comment` and `scheduled` event types are required for this Action
# to work properly.
on:
  workflow_dispatch:
  # issue_comment:
  #   types: [created]
  # schedule:
  #   # Schedule for ten minutes after the hour, every hour
  #   - cron: '* */12 * * *'

# By specifying the access of one of the scopes, all of those that are not
# specified are set to 'none'.
permissions:
  issues: write

jobs:
  noResponse:
    runs-on: ubuntu-latest
    steps:
      - uses: lee-dohm/no-response@v0.5.0
        with:
          token: ${{ github.token }}
          daysUntilClose: 14
          responseRequiredLabel: 'Status: Needs Info'
          closeComment: >
                This issue has been automatically closed because there has been no response
                to our request for more information from the original author. With only the
                information that is currently in the issue, we don't have enough information
                to take action. Please reach out if you have or find the answers we need so
                that we can investigate further.
