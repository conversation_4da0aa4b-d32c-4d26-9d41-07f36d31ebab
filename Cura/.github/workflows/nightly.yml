name: Nightly build
run-name: Nightly build

on:
  workflow_call:
    inputs:
      cura_conan_version:
        required: true
        type: string
      release_tag:
        required: true
        type: string
      caller_workflow:
        required: true
        type: string


jobs:
  create-installers:
    name: Create installers
    uses: wsd07/cura-workflows/.github/workflows/cura-installers.yml@main
    with:
      cura_conan_version: ${{ inputs.cura_conan_version }}
    secrets: inherit

  update-nightly-release:
    name: Upload installers
    runs-on: ubuntu-latest
    needs: [ create-installers ]
    steps:
      - name: Setup the build environment
        uses: wsd07/cura-workflows/.github/actions/setup-build-environment@main

      - name: Download installers jobs artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: UltiMaker-Cura-*
          path: installers
          merge-multiple: true

      - name: Rename the installers
        id: rename-installers
        working-directory: installers
        run: python ../Cura-workflows/runner_scripts/rename_installers.py --tag "nightly" >> $GITHUB_OUTPUT

      - name: create the release notes
        shell: python
        run: |
          import os
          import datetime
          from jinja2 import Template
          
          with open(".github/workflows/nightly_release_notes.md.jinja", "r") as f:
              release_notes = Template(f.read())
          
          short_version = "${{ steps.rename-installers.outputs.short_version }}"
          caller_workflow = "${{ inputs.caller_workflow }}"
          is_main = os.getenv("GITHUB_REF") == "refs/heads/main"

          with open("release-notes.md", "w") as f:
              f.write(release_notes.render(
                timestamp=str(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                caller_workflow=caller_workflow,
                branch_specific="" if is_main else f"?branch={short_version}",
              ))

      - name: Update nightly release description and binaries
        run: |
          gh release edit ${{ inputs.release_tag }} --title "${{ steps.rename-installers.outputs.cura_version }}" --notes-file release-notes.md

          for file in "installers/*"; do
            gh release upload ${{ inputs.release_tag }} $file --clobber
          done
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
