name: Find packages for <PERSON>ra ticket and create installers

on:
  workflow_dispatch:
    inputs:
      jira_ticket_number:
        description: 'Jira ticket number for Conan package discovery (e.g., cura_12345)'
        required: true
        type: string
      start_builds:
        default: false
        required: false
        type: boolean
      conan_args:
        description: 'Conan args'
        default: ''
        type: string
      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        type: boolean
      staging:
        description: 'Use staging API'
        default: false
        type: boolean

permissions:
  contents: read

jobs:
  find-packages:
    name: Find packages for Jira ticket
    uses: wsd07/cura-workflows/.github/workflows/find-package-by-ticket.yml@main
    with:
      jira_ticket_number: ${{ inputs.jira_ticket_number }}
    secrets: inherit

  installers:
    name: Create installers
    needs: find-packages
    if: ${{ inputs.start_builds == true && needs.find-packages.outputs.discovered_packages != '' }}
    uses: wsd07/cura-workflows/.github/workflows/cura-installers.yml@main
    with:
      cura_conan_version: ${{ needs.find-packages.outputs.cura_package }}
      package_overrides: ${{ needs.find-packages.outputs.package_overrides }}
      conan_args: ${{ inputs.conan_args }}
      enterprise: ${{ inputs.enterprise }}
      staging: ${{ inputs.staging  }}
    secrets: inherit
