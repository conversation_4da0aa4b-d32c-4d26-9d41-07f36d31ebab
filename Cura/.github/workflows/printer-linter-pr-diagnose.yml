name: printer-linter-pr-diagnose

on:
  pull_request:
    paths:
      - "resources/**"

permissions:
  contents: read

jobs:
  printer-linter-diagnose:
    name: Printer linter PR diagnose

    runs-on: ubuntu-latest
    steps:
      - name: Setup the build environment
        uses: wsd07/cura-workflows/.github/actions/setup-build-environment@main

      - uses: greguintow/get-diff-action@v7
        with:
          DIFF_FILTER: AMRCD
          PATTERNS: |
            resources/+(extruders|definitions)/*.def.json
            resources/+(intent|quality|variants)/**/*.inst.cfg

      - name: Create results directory
        run: mkdir printer-linter-result

      - name: Diagnose file(s)
        if: env.GIT_DIFF && !env.MATCHED_FILES
        run: python printer-linter/src/terminal.py --diagnose --report printer-linter-result/fixes.yml ${{ env.GIT_DIFF_FILTERED }}

      - name: Check Deleted Files(s)
        if: env.GIT_DIFF
        run: python printer-linter/src/terminal.py --deleted --report printer-linter-result/comment.md ${{ env.GIT_DIFF_FILTERED }}

      - name: Save PR metadata
        run: |
          echo ${{ github.event.number }} > printer-linter-result/pr-id.txt
          echo ${{ github.event.pull_request.head.repo.full_name }} > printer-linter-result/pr-head-repo.txt
          echo ${{ github.event.pull_request.head.sha }} > printer-linter-result/pr-head-sha.txt

      - uses: actions/upload-artifact@v4
        with:
          name: printer-linter-result
          path: printer-linter-result/

      - name: Run clang-tidy-pr-comments action
        uses: platisd/clang-tidy-pr-comments@bc0bb7da034a8317d54e7fe1e819159002f4cc40
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          clang_tidy_fixes: result.yml
          request_changes: true
