name: Quick Test
run-name: Quick dependency test by @${{ github.actor }}

on:
  workflow_dispatch:

jobs:
  test-dependencies:
    name: Test Dependencies
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout Cura
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Test Python installation
        run: |
          python --version
          python -c "import sys; print('Python path:', sys.executable)"

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install conan==2.7.0
          pip install wheel setuptools
          pip install GitPython
          pip install requests
          pip install pyyaml
          pip install jinja2

      - name: Test imports
        run: |
          python -c "import git; print('GitPython OK')"
          python -c "import requests; print('requests OK')"
          python -c "import yaml; print('pyyaml OK')"
          python -c "import jinja2; print('jinja2 OK')"
          python -c "from conan import <PERSON><PERSON><PERSON>; print('Conan OK')"

      - name: Test Conan setup
        run: |
          conan --version
          conan profile detect --force
          conan profile show default

      - name: Test conan-config
        run: |
          conan config install https://github.com/wsd07/conan-config.git
          echo "Conan config installed successfully"

      - name: Test conanfile.py parsing
        run: |
          python -c "
          import sys
          sys.path.append('.')
          try:
              from conanfile import CuraConan
              print('conanfile.py imports successfully')
              conan = CuraConan()
              print('CuraConan class instantiated successfully')
          except Exception as e:
              print(f'Error: {e}')
              sys.exit(1)
          "

      - name: Test basic conan commands
        run: |
          conan install . --build=missing --dry-run
          echo "Conan dry-run completed successfully"
