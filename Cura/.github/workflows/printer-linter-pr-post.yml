name: printer-linter-pr-post

on:
  workflow_dispatch:
  # workflow_run:
  #   workflows: ["printer-linter-pr-diagnose"]
  #   types: [completed]

jobs:
  printer-linter-result:
    # Trigger the job only if the previous (insecure) workflow completed successfully
    if: ${{ github.event.workflow_run.event == 'pull_request' && github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Download analysis results
        uses: actions/github-script@v7
        with:
          script: |
            const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
                owner: context.repo.owner,
                repo: context.repo.repo,
                run_id: ${{github.event.workflow_run.id }},
            });
            const matchArtifact = artifacts.data.artifacts.filter((artifact) => {
                return artifact.name == "printer-linter-result"
            })[0];
            const download = await github.rest.actions.downloadArtifact({
                owner: context.repo.owner,
                repo: context.repo.repo,
                artifact_id: matchArtifact.id,
                archive_format: "zip",
            });
            const fs = require("fs");
            fs.writeFileSync("${{ github.workspace }}/printer-linter-result.zip", Buffer.from(download.data));

      - name: Extract analysis results
        run: |
          mkdir printer-linter-result
          unzip -j printer-linter-result.zip -d printer-linter-result

      - name: Set PR details environment variables
        uses: actions/github-script@v7
        with:
          script: |
            const assert = require("node:assert").strict;
            const fs = require("fs");
            function exportVar(varName, fileName, regEx) {
                const val = fs.readFileSync("${{ github.workspace }}/printer-linter-result/" + fileName, {
                    encoding: "ascii"
                }).trimEnd();
                assert.ok(regEx.test(val), "Invalid value format for " + varName);
                core.exportVariable(varName, val);
            }
            exportVar("PR_ID", "pr-id.txt", /^[0-9]+$/);
            exportVar("PR_HEAD_REPO", "pr-head-repo.txt", /^[-./0-9A-Z_a-z]+$/);
            exportVar("PR_HEAD_SHA", "pr-head-sha.txt", /^[0-9A-Fa-f]+$/);
            fs.access("${{ github.workspace }}/printer-linter-result/comment.md", fs.constants.F_OK, (err) => {
                if (err) {
                    core.exportVariable("commentFileExists", "false");
                } else {
                    core.exportVariable("commentFileExists", "true");
                }
            });

      - uses: actions/checkout@v4
        with:
          repository: ${{ env.PR_HEAD_REPO }}
          ref: ${{ env.PR_HEAD_SHA }}
          persist-credentials: false

      - name: Redownload analysis results
        uses: actions/github-script@v7
        with:
          script: |
            const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
                owner: context.repo.owner,
                repo: context.repo.repo,
                run_id: ${{github.event.workflow_run.id }},
            });
            const matchArtifact = artifacts.data.artifacts.filter((artifact) => {
                return artifact.name == "printer-linter-result"
            })[0];
            const download = await github.rest.actions.downloadArtifact({
                owner: context.repo.owner,
                repo: context.repo.repo,
                artifact_id: matchArtifact.id,
                archive_format: "zip",
            });
            const fs = require("fs");
            fs.writeFileSync("${{ github.workspace }}/printer-linter-result.zip", Buffer.from(download.data));

      - name: Extract analysis results
        run: |
          mkdir printer-linter-result
          unzip -j printer-linter-result.zip -d printer-linter-result

      - name: Run PR Comments
        if: env.commentFileExists == 'true'
        uses: peter-evans/create-or-update-comment@v4
        with:
          issue-number: ${{ env.PR_ID }}
          body-path: 'printer-linter-result/comment.md'

      - name: Run clang-tidy-pr-comments action
        uses: platisd/clang-tidy-pr-comments@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          clang_tidy_fixes: printer-linter-result/fixes.yml
          pull_request_id: ${{ env.PR_ID }}
          request_changes: true
