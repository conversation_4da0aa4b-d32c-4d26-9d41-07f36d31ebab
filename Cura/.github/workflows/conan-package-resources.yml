name: conan-package-resources

on:
  workflow_dispatch:
  # push:
  #   paths:
  #     - '.github/workflows/conan-package-resources.yml'
  #     - 'resources/definitions/**'
  #     - 'resources/extruders/**'
  #     - 'resources/images/**'
  #     - 'resources/intent/**'
  #     - 'resources/meshes/**'
  #     - 'resources/quality/**'
  #     - 'resources/variants/**'
  #     - 'resources/conanfile.py'
  #     - 'resources/conandata.yml'
  #   branches:
  #     - 'main'
  #     - 'CURA-*'
  #     - 'PP-*'
  #     - 'NP-*'
  #     - '[0-9].[0-9]*'
  #     - '[0-9].[0-9][0-9]*'

jobs:
  conan-package:
    uses: wsd07/cura-workflows/.github/workflows/conan-package.yml@main
    with:
      conan_recipe_root: "./resources/"
      platform_windows: false
      platform_mac: false
      install_system_dependencies: false
    secrets: inherit

  signal-curator:
    needs: conan-package
    runs-on: ubuntu-latest
    steps:
    - name: Trigger Curator Workflow
      run: |
        gh workflow run --repo ultimaker/curator -r main package.yml
      env:
        GITHUB_TOKEN: ${{ secrets.CURATOR_TRIGGER_PAT_C3PO }}
