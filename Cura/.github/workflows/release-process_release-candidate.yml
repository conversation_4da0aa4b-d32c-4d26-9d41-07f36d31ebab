name: Prepare Release Candidate
run-name: Release Candidate for Cura ${{ inputs.cura_version }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      cura_version:
        description: 'Cura version number, e.g. 5.7.0, 5.7.2 or 5.8.0-beta.2'
        required: true
        type: string

      publish_release_description:
        description: 'Create the GitHub release (if existing, the description will be overridden based on the changelog)'
        required: true
        type: boolean

jobs:
  parse-version:
    name: Parse input version string
    runs-on: ubuntu-latest
    outputs:
      version_major: ${{ steps.version_parser.outputs.major }}
      version_minor: ${{ steps.version_parser.outputs.minor }}
      version_patch: ${{ steps.version_parser.outputs.patch }}
      branch_name: ${{ steps.version_parser.outputs.major }}.${{ steps.version_parser.outputs.minor }}
    steps:
      - name: Parse version string
        id: version_parser
        uses: booxmedialtd/ws-action-parse-semver@v1.4.7
        with:
          input_string: ${{ inputs.cura_version }}

  freeze-packages-versions:
    name: Freeze packges versions
    uses: Ultimaker/Cura-workflows/.github/workflows/cura-set-packages-versions.yml@main
    needs: [parse-version]
    with:
      cura_version: ${{ inputs.cura_version }}
      branch: ${{ needs.parse-version.outputs.branch_name }}
      create_feature_branch: false
    secrets: inherit

  find-rc-tag:
    name: Find RC tag name
    runs-on: ubuntu-latest
    needs: [freeze-packages-versions]
    outputs:
      tag_name: ${{ steps.find-available-tag-name.outputs.tag_name }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-tags: true
          fetch-depth: 0

      - name: Find available tag name
        id: find-available-tag-name
        run: |
          VERSION=${{ inputs.cura_version }}

          RC_INDEX=0
          while
            RC_INDEX=$((RC_INDEX+1))
            TAG_NAME="$VERSION-RC$RC_INDEX"
            [[ $(git tag -l "$TAG_NAME") ]]
          do true; done

          echo "tag_name=$TAG_NAME" >> "$GITHUB_OUTPUT"

  create-tags:
    name: Create tags
    runs-on: ubuntu-latest
    needs: [parse-version, find-rc-tag]
    outputs:
      main_commit: ${{ steps.export-main-commit.outputs.main_commit }}
    strategy:
      matrix:
        repository: [Cura, Uranium, CuraEngine, cura-binary-data, fdm_materials]
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          repository: Ultimaker/${{ matrix.repository }}
          ref: ${{ needs.parse-version.outputs.branch_name }}
          token: ${{ secrets.CURA_AUTORELEASE_PAT }}

      - name: Create RC tag
        run: |
          git tag ${{ needs.find-rc-tag.outputs.tag_name }}
          git push origin tag ${{ needs.find-rc-tag.outputs.tag_name }}

      - name: Create or update release tag
        run: |
          git tag -f ${{ inputs.cura_version }}
          git push -f origin tag ${{ inputs.cura_version }}

      - name: Export Cura tagged commit
        id: export-main-commit
        if: ${{ matrix.repository == 'Cura' }}
        run: |
          echo "main_commit=`git rev-parse HEAD`" >> "$GITHUB_OUTPUT"

  create-packages:
    name: Create conan packages
    uses: wsd07/cura-workflows/.github/workflows/conan-package-release.yml@main
    needs: [parse-version, freeze-packages-versions]
    strategy:
      matrix:
        repository: [Cura, Uranium, CuraEngine, cura-binary-data, fdm_materials]
        include:
          - conan_recipe_root: "."
          - repository: Cura
            conan_recipe_root: "resources"
    with:
      repository: ${{ matrix.repository }}
      branch: ${{ needs.parse-version.outputs.branch_name }}
      conan_recipe_root: ${{ matrix.conan_recipe_root }}
    secrets: inherit

  create-installers:
    name: Create installers
    uses: wsd07/cura-workflows/.github/workflows/cura-installers.yml@main
    needs: [parse-version, create-packages]
    with:
      cura_conan_version: cura/${{ inputs.cura_version }}
      conan_args: "-c user.sentry:environment=production"
    secrets: inherit

  create-release-draft:
    name: Create the release draft
    runs-on: ubuntu-latest
    needs: [create-installers, parse-version, create-tags]
    steps:
      - name: Checkout Cura repo
        uses: actions/checkout@v4
        with:
          ref: ${{ needs.parse-version.outputs.branch_name }}

      - name: Extract changelog
        if: ${{ inputs.publish_release_description }}
        run: python ./scripts/extract_changelog.py --version ${{ needs.parse-version.outputs.version_major }}.${{ needs.parse-version.outputs.version_minor }}.${{ needs.parse-version.outputs.version_patch }} --changelog ./resources/texts/change_log.txt > formatted_changelog.txt

      - name: Create release
        uses: notpeelz/action-gh-create-release@v5.0.1
        if: ${{ inputs.publish_release_description }}
        with:
          target: ${{ needs.create-tags.outputs.main_commit }}
          tag: ${{ inputs.cura_version }}
          strategy: replace
          title: UltiMaker Cura ${{ inputs.cura_version }}
          draft: true
          body-source: file
          body: formatted_changelog.txt

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
          merge-multiple: true

      - name: Upload artifacts
        working-directory: artifacts
        run: |
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-linux-X64.AppImage --clobber
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-linux-X64.AppImage.asc --clobber
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-macos-ARM64.dmg --clobber
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-macos-ARM64.pkg --clobber
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-macos-X64.dmg --clobber
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-macos-X64.pkg --clobber
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-win64-X64.exe --clobber
          gh release upload ${{ inputs.cura_version }} UltiMaker-Cura-${{ inputs.cura_version }}-win64-X64.msi --clobber
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
