name: conan-package

on:
  workflow_dispatch:
  # push:
  #   paths:
  #     - 'plugins/**'
  #     - 'cura/**'
  #     - 'resources/bundled_packages/**'
  #     - 'resources/i18n/**'
  #     - 'resources/qml/**'
  #     - 'resources/setting_visibility/**'
  #     - 'resources/shaders/**'
  #     - 'resources/texts/**'
  #     - 'resources/themes/**'
  #     - 'resources/public_key.pem'
  #     - 'resources/README_resources.txt'
  #     - 'icons/**'
  #     - 'tests/**'
  #     - 'packaging/**'
  #     - '.github/workflows/conan-package.yml'
  #     - '.github/workflows/notify.yml'
  #     - '.github/workflows/requirements-runner.txt'
  #     - 'requirements*.txt'
  #     - 'conanfile.py'
  #     - 'conandata.yml'
  #     - '*.jinja'
  #   branches:
  #     - 'main'
  #     - 'CURA-*'
  #     - 'PP-*'
  #     - 'NP-*'
  #     - '[0-9].[0-9]*'
  #     - '[0-9].[0-9][0-9]*'

jobs:
  conan-package:
    uses: wsd07/cura-workflows/.github/workflows/conan-package.yml@main
    secrets: inherit
