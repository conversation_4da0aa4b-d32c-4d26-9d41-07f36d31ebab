name: All installers
run-name: ${{ inputs.cura_conan_version }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      cura_conan_version:
        description: 'Cura Conan Version'
        default: ''
        type: string
      package_overrides:
        description: 'List of specific packages to be used (space-separated)'
        default: ''
        type: string
      conan_args:
        description: 'Conan args'
        default: ''
        type: string
      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        type: boolean
      staging:
        description: 'Use staging API'
        default: false
        type: boolean


jobs:
  installers:
    name: Create installers
    uses: wsd07/cura-workflows/.github/workflows/cura-installers.yml@main
    with:
      cura_conan_version: ${{ inputs.cura_conan_version }}
      package_overrides: ${{ inputs.package_overrides }}
      conan_args: ${{ inputs.conan_args }}
      enterprise: ${{ inputs.enterprise }}
      staging: ${{ inputs.staging  }}
    secrets: inherit
