name: Build Test
run-name: Test build by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      os:
        description: 'Operating system to test'
        default: 'ubuntu-22.04'
        type: choice
        options:
          - ubuntu-22.04
          - windows-2022
          - macos-12
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  CONAN_USER_HOME: "${{ github.workspace }}/conan-cache"
  CONAN_HOME: "${{ github.workspace }}/conan-cache/.conan2"

jobs:
  build-test:
    name: Build Test
    runs-on: ${{ inputs.os || 'ubuntu-22.04' }}
    strategy:
      matrix:
        os: [ubuntu-22.04]
    steps:
      - name: Checkout Cura
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: Install system dependencies (Linux)
        if: runner.os == 'Linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            build-essential \
            cmake \
            git \
            libegl1-mesa-dev \
            libfontconfig1-dev \
            libfreetype6-dev \
            libgl1-mesa-dev \
            libglib2.0-dev \
            libglu1-mesa-dev \
            libharfbuzz-dev \
            libicu-dev \
            libnss3-dev \
            libpng-dev \
            libssl-dev \
            libx11-dev \
            libx11-xcb-dev \
            libxcb1-dev \
            libxext-dev \
            libxfixes-dev \
            libxi-dev \
            libxrender-dev \
            libxss1 \
            libgstreamer-gl1.0-0 \
            libgstreamer-plugins-base1.0-0

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install conan==2.7.0
          pip install wheel setuptools
          pip install GitPython
          pip install requests
          pip install pyyaml
          pip install jinja2

      - name: Setup Conan
        run: |
          conan profile detect --force

      - name: Cache Conan packages
        uses: actions/cache@v3
        with:
          path: ${{ env.CONAN_HOME }}
          key: conan-${{ runner.os }}-${{ hashFiles('**/conanfile.py', '**/conandata.yml') }}
          restore-keys: |
            conan-${{ runner.os }}-

      - name: Install dependencies
        run: |
          conan install . --build=missing --update

      - name: Build Cura
        run: |
          conan build .

      - name: Run tests
        run: |
          python -m pytest tests/ -v

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ runner.os }}
          path: |
            build/
          retention-days: 7
