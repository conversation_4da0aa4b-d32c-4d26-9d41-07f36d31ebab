name: Release Build
run-name: Release ${{ inputs.version }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., 5.8.0)'
        required: true
        type: string
      prerelease:
        description: 'Mark as pre-release'
        default: false
        type: boolean
      draft:
        description: 'Create as draft release'
        default: true
        type: boolean

env:
  CONAN_USER_HOME: "${{ github.workspace }}/conan-cache"
  CONAN_HOME: "${{ github.workspace }}/conan-cache/.conan2"

jobs:
  build-all-platforms:
    name: Build All Platforms
    strategy:
      matrix:
        include:
          - os: windows-2022
            platform: windows
            artifact_pattern: "*.exe,*.msi"
          - os: macos-12
            platform: macos
            artifact_pattern: "*.dmg,*.pkg"
          - os: ubuntu-22.04
            platform: linux
            artifact_pattern: "*.AppImage,*.deb,*.rpm"
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout Cura
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: Install system dependencies (Linux)
        if: runner.os == 'Linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            build-essential \
            cmake \
            git \
            libegl1-mesa-dev \
            libfontconfig1-dev \
            libfreetype6-dev \
            libgl1-mesa-dev \
            libglib2.0-dev \
            libglu1-mesa-dev \
            libharfbuzz-dev \
            libicu-dev \
            libnss3-dev \
            libpng-dev \
            libssl-dev \
            libx11-dev \
            libx11-xcb-dev \
            libxcb1-dev \
            libxext-dev \
            libxfixes-dev \
            libxi-dev \
            libxrender-dev \
            libxss1 \
            libgstreamer-gl1.0-0 \
            libgstreamer-plugins-base1.0-0

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install conan==2.7.0
          pip install wheel setuptools

      - name: Setup Conan
        run: |
          conan profile detect --force

      - name: Cache Conan packages
        uses: actions/cache@v3
        with:
          path: ${{ env.CONAN_HOME }}
          key: conan-${{ matrix.platform }}-${{ hashFiles('**/conanfile.py', '**/conandata.yml') }}
          restore-keys: |
            conan-${{ matrix.platform }}-

      - name: Install dependencies
        run: |
          conan install . --build=missing --update

      - name: Build Cura
        run: |
          conan build .

      - name: Create installer
        run: |
          conan create . --build=missing

      - name: Upload installer
        uses: actions/upload-artifact@v4
        with:
          name: installer-${{ matrix.platform }}
          path: |
            build/Release/${{ matrix.artifact_pattern }}
          retention-days: 30

  create-release:
    name: Create Release
    needs: build-all-platforms
    runs-on: ubuntu-latest
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: installers

      - name: Display structure of downloaded files
        run: ls -la installers/

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ inputs.version }}
          name: Cura ${{ inputs.version }}
          draft: ${{ inputs.draft }}
          prerelease: ${{ inputs.prerelease }}
          files: |
            installers/**/*
          body: |
            ## Cura ${{ inputs.version }}
            
            This is a custom build of Cura ${{ inputs.version }}.
            
            ### Downloads
            - **Windows**: `.exe` or `.msi` files
            - **macOS**: `.dmg` or `.pkg` files  
            - **Linux**: `.AppImage`, `.deb`, or `.rpm` files
            
            ### Changes
            - Custom build based on Ultimaker Cura
            - Built from commit: ${{ github.sha }}
            
            ### Installation
            Download the appropriate file for your operating system and follow the standard installation process.
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
