name: unit-test

on:
  push:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'cura/**'
      - 'icons/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/requirements-runner.txt'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - '[0-9]+.[0-9]+'

  pull_request:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'cura/**'
      - 'icons/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/requirements-runner.txt'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - '[0-9]+.[0-9]+'

jobs:
  testing:
    name: Run unit tests
    uses: wsd07/cura-workflows/.github/workflows/unit-test.yml@main
    with:
      test_use_pytest: true
