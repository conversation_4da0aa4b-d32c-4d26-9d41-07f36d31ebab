name: printer-linter-format

on:
  push:
    paths:
      - 'resources/definitions/**'
      - 'resources/extruders/**'
      - 'resources/intent/**'
      - 'resources/quality/**'
      - 'resources/variants/**'

jobs:
  printer-linter-format:
    name: Printer linter auto format
    uses: wsd07/cura-workflows/.github/workflows/lint-formatter.yml@main
    with:
      file_patterns: |
        resources/+(definitions|extruders)/*.def.json
        resources/+(intent|quality|variants)/**/*.inst.cfg
      command: python printer-linter/src/terminal.py --format
      commit_message: "Apply printer-linter format"
