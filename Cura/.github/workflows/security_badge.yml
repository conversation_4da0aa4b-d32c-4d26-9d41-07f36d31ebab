# NOTE: Best to keep all of these remarks in, they might prove useful in the future.
#       This is basically just the standard one that is suggested on 'new workflow'.

name: Scorecard supply-chain security
on:
  workflow_dispatch:
  # For Branch-Protection check. Only the default branch is supported. See
  # https://github.com/ossf/scorecard/blob/main/docs/checks.md#branch-protection
  # branch_protection_rule:
  # To guarantee Maintained check is occasionally updated. See
  # https://github.com/ossf/scorecard/blob/main/docs/checks.md#maintained
  # schedule:
  #   - cron: '25 2 * * 5'
  # push:
  #   branches: [ "main" ]

# Declare default permissions as read only.
permissions: read-all

jobs:
  analysis:
    name: Scorecard analysis
    runs-on: ubuntu-latest
    permissions:
      # Needed for Code scanning upload
      security-events: write
      # Needed for GitHub OIDC token if publish_results is true
      id-token: write

    steps:
      - name: "Checkout code"
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
        with:
          persist-credentials: false

      - name: "Run analysis"
        uses: ossf/scorecard-action@0864cf19026789058feabb7e87baa5f140aac736 # v2.3.1
        with:
          results_file: results.sarif
          results_format: sarif
          # Scorecard team runs a weekly scan of public GitHub repos,
          # see https://github.com/ossf/scorecard#public-data.
          # Setting `publish_results: true` helps us scale by leveraging your workflow to
          # extract the results instead of relying on our own infrastructure to run scans.
          # And it's free for you!
          publish_results: true

      # Upload the results as artifacts (optional). Commenting out will disable
      # uploads of run results in SARIF format to the repository Actions tab.
      # https://docs.github.com/en/actions/advanced-guides/storing-workflow-data-as-artifacts
      - name: "Upload artifact"
        uses: actions/upload-artifact@5d5d22a31266ced268874388b861e4b58bb5c2f3 # v4.3.1
        with:
          name: SARIF file
          path: results.sarif
          retention-days: 5

      # Upload the results to GitHub's code scanning dashboard (optional).
      # Commenting out will disable upload of results to your repo's Code Scanning dashboard
      - name: "Upload to code-scanning"
        uses: github/codeql-action/upload-sarif@83a02f7883b12e0e4e1a146174f5e2292a01e601 # v2.16.4
        with:
          sarif_file: results.sarif
