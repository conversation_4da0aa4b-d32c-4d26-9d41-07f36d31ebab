{"version": 2, "name": "Cubicon Common", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "Cubicon R&D Center", "manufacturer": "Cubicon"}, "overrides": {"adhesion_type": {"default_value": "raft"}, "bottom_thickness": {"value": "top_bottom_thickness * 0.6"}, "coasting_volume": {"default_value": 0.032}, "cool_min_layer_time": {"default_value": 15}, "cool_min_layer_time_fan_speed_max": {"default_value": 15}, "infill_before_walls": {"default_value": false}, "infill_sparse_density": {"default_value": 20}, "layer_height": {"default_value": 0.2}, "layer_height_0": {"default_value": 0.2}, "machine_end_gcode": {"default_value": "M104 S0\nM140 S0\nM904\nM117 Print completed! \nM84"}, "machine_gcode_flavor": {"default_value": "<PERSON><PERSON><PERSON>"}, "machine_heated_bed": {"default_value": true}, "machine_start_gcode": {"default_value": "M201 X400 Y400\nM202 X400 Y400\nG28 ; Home\nG1 Z15.0 F6000 ;move the platform down 15mm\n;Prime the extruder\nG92 E0\nG1 F200 E3\nG92 E0"}, "material_flow_layer_0": {"default_value": 100}, "max_skin_angle_for_expansion": {"default_value": 90}, "retraction_amount": {"default_value": 1.5}, "skin_angles": {"default_value": "[135, 45]"}, "support_interface_pattern": {"default_value": "zigzag"}, "support_pattern": {"default_value": "zigzag"}, "top_bottom_pattern": {"default_value": "lines"}, "top_bottom_thickness": {"default_value": 1.0}, "wall_thickness": {"value": "1.2"}}}