{"version": 2, "name": "Galaxy 600", "inherits": "atmat_signal_pro_base", "metadata": {"visible": true, "quality_definition": "atmat_signal_pro_base"}, "overrides": {"acceleration_layer_0": {"value": "250"}, "acceleration_prime_tower": {"value": "750"}, "acceleration_print": {"value": "750"}, "acceleration_support": {"value": "750"}, "acceleration_support_interface": {"value": "750"}, "acceleration_topbottom": {"value": "750"}, "acceleration_travel": {"value": "750"}, "acceleration_wall": {"value": "750"}, "acceleration_wall_0": {"value": "500"}, "jerk_layer_0": {"value": "5"}, "jerk_prime_tower": {"value": "jerk_print"}, "jerk_print": {"value": "7.5"}, "jerk_support": {"value": "jerk_print"}, "jerk_support_interface": {"value": "jerk_print"}, "jerk_topbottom": {"value": "jerk_print"}, "jerk_travel": {"value": "jerk_layer_0"}, "jerk_travel_layer_0": {"value": "jerk_layer_0"}, "jerk_wall": {"value": "jerk_print"}, "jerk_wall_0": {"value": "jerk_print"}, "machine_depth": {"default_value": 500}, "machine_height": {"default_value": 600}, "machine_name": {"default_value": "Galaxy 600"}, "machine_width": {"default_value": 500}}}