{"version": 2, "name": "Uni Base Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "<PERSON><PERSON><PERSON> (C)", "manufacturer": "Uni 3D", "file_formats": "text/x-gcode", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "imade3d_petg", "imade3d_pla", "innofill_innoflex60", "verbatim_bvoh"], "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "uni_extruder_1st", "1": "uni_extruder_2nd"}, "preferred_material": "generic_abs_175", "preferred_quality_type": "q020", "preferred_variant_name": "0.40mm Nozzle", "variants_name": "Nozzle Size"}, "overrides": {"adhesion_type": {"value": "'brim'"}, "bottom_thickness": {"value": "layer_height*3 if layer_height > 0.15 else 0.8"}, "bridge_settings_enabled": {"value": "True"}, "brim_width": {"value": "5"}, "cool_fan_full_at_height": {"value": "layer_height*2"}, "cool_lift_head": {"value": "True"}, "cool_min_layer_time": {"value": "5"}, "cool_min_layer_time_fan_speed_max": {"value": "20"}, "cool_min_speed": {"value": "15"}, "expand_skins_expand_distance": {"value": "3"}, "gantry_height": {"value": 55}, "infill_before_walls": {"value": "False"}, "infill_enable_travel_optimization": {"value": "True"}, "infill_line_width": {"value": "round(line_width * 1.1, 2)"}, "infill_pattern": {"value": "'gyroid'"}, "layer_height_0": {"value": "layer_height"}, "machine_buildplate_type": {"value": "'glass'"}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 220}, "machine_end_gcode": {"default_value": "G91\nG1 E-1 F600\nG1 Z+1 E-3 X-20 Y-20 F4800\nM104 S0\nM106 S0\nM140 S0\nM220 S100\nM221 S100\nG28\nG90\nM84\n;\n"}, "machine_extruder_count": {"value": 2}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-10, 10], [-10, -10], [10, -10], [10, 10]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 250}, "machine_name": {"default_value": "UNI Base Printer"}, "machine_shape": {"default_value": "rectangular"}, "machine_start_gcode": {"default_value": ";Sliced at: {day} {date} {time}\nG21\nG90\nM82\nM220 S100\nM221 S100\nG28\nM190 S{material_bed_temperature_layer_0}\nM109 S{material_print_temperature_layer_0}\nG1 F600 Z15\nG92 E0\nG1 F300 E10\nM117 Printing...\n;\n"}, "machine_width": {"default_value": 270}, "material_diameter": {"default_value": 1.75}, "optimize_wall_printing_order": {"value": "True"}, "retraction_amount": {"value": "4"}, "retraction_combing": {"value": "'all'"}, "retraction_combing_max_distance": {"value": "10"}, "retraction_min_travel": {"value": "3"}, "skin_line_width": {"value": "round(line_width * 1.0, 2)"}, "skin_outline_count": {"value": "0"}, "skirt_brim_line_width": {"value": "round(line_width * 1.1, 2)"}, "speed_layer_0": {"value": "25"}, "speed_print": {"value": "80"}, "speed_topbottom": {"value": "50"}, "speed_travel_layer_0": {"value": "40"}, "support_angle": {"value": "65"}, "support_brim_enable": {"value": "True"}, "support_enable": {"value": "True"}, "support_infill_rate": {"value": "20"}, "support_offset": {"value": "2"}, "top_bottom_pattern": {"value": "'zigzag'"}, "top_bottom_pattern_0": {"value": "'zigzag'"}, "travel_avoid_other_parts": {"value": "False"}, "z_seam_type": {"value": "'shortest'"}, "zig_zaggify_infill": {"value": "True"}}}