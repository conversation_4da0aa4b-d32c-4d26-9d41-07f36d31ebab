{"version": 2, "name": "ELEGOO NEPTUNE 4 Max", "inherits": "elegoo_neptune_4", "metadata": {"visible": true, "author": "mastercaution", "platform": "elegoo_platform_max.3mf", "platform_offset": [-2.1, -0.2, 0], "quality_definition": "elegoo_neptune_4"}, "overrides": {"acceleration_print": {"maximum_value_warning": "15000", "value": 2500}, "machine_depth": {"default_value": 430}, "machine_height": {"default_value": 482}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 15000}, "machine_max_acceleration_y": {"value": 15000}, "machine_name": {"default_value": "ELEGOO NEPTUNE 4 Max"}, "machine_start_gcode": {"default_value": ";ELEGOO NEPTUNE 4 MAX\nM220 S100 ;Set the feed speed to 100%\nM221 S100 ;Set the flow rate to 100%\nM104 S140 ;Start heating extruder\nM190 S{material_bed_temperature_layer_0} ;Wait for the bed to reach print temp\nG90\nG28 ;home\nG1 Z10 F300\nG1 X165 Y0 F6000\nG1 Z0 F300\nM109 S{material_print_temperature_layer_0} ;Wait for extruder to reach print temp\nG92 E0 ;Reset Extruder\nG1 X165 Y0 Z0.4 F300 ;Move to start position\nG1 X265 E30 F400 ;Draw the first line\nG1 Z0.6 F120.0 ;Move to side a little\nG1 X260 F3000\nG92 E0 ;Reset Extruder"}, "machine_width": {"default_value": 430}, "nozzle_disallowed_areas": {"default_value": [[[-215, -215], [-215, 215], [-211, 215], [-211, -215]], [[215, 215], [215, -215], [211, -215], [211, 215]], [[-215, -215], [215, -215], [-215, -211], [215, -211]], [[-215, 215], [215, 215], [-215, 211], [215, 211]]]}}}