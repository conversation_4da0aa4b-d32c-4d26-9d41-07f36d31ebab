{"version": 2, "name": "Biqu Base Printer", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "<PERSON>", "manufacturer": "<PERSON><PERSON><PERSON>", "file_formats": "text/x-gcode", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "biqu_base_extruder_0"}, "preferred_material": "generic_pla_175", "preferred_quality_type": "standard", "preferred_variant_name": "0.4mm Nozzle", "variants_name": "Nozzle Diameter"}, "overrides": {"acceleration_enabled": {"value": false}, "acceleration_print": {"value": 500}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": 500}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "brim_replaces_support": {"value": false}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "fill_outline_gaps": {"value": false}, "infill_before_walls": {"value": false}, "infill_line_width": {"value": "line_width * 1.2"}, "infill_overlap": {"value": 30.0}, "infill_pattern": {"value": "'lines' if infill_sparse_density > 50 else 'cubic'"}, "infill_wipe_dist": {"value": 0.0}, "jerk_enabled": {"value": false}, "jerk_print": {"value": 8}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 500}, "machine_end_gcode": {"default_value": "                               ;BIQU Default End Gcode\nG91                            ;Relative positioning\nG1 E-2 F2700                   ;Retract a bit\nG1 E-2 Z0.2 F2400              ;Retract a bit more and raise Z\nG1 X5 Y5 F3000                 ;Wipe out\nG1 Z10                         ;Raise Z by 10mm\nG90                            ;Return to absolute positioning\n\nG1 X0 Y{machine_depth}         ;TaDaaaa\nM106 S0                        ;Turn-off fan\nM104 S0                        ;Turn-off hotend\nM140 S0                        ;Turn-off bed\n\nM84 X Y E                      ;Disable all steppers but Z\n"}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 500}, "machine_max_acceleration_y": {"value": 500}, "machine_max_acceleration_z": {"value": 100}, "machine_max_feedrate_e": {"value": 75}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 10}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.4}, "machine_name": {"default_value": "BIQU Base Printer"}, "machine_start_gcode": {"default_value": "M201 X500.00 Y500.00 Z100.00 E5000.00 ;Setup machine max acceleration\nM203 X500.00 Y500.00 Z10.00 E50.00 ;Setup machine max feedrate\nM204 P500.00 R1000.00 T500.00 ;Setup Print/Retract/Travel acceleration\nM205 X8.00 Y8.00 Z0.40 E5.00 ;Setup Jerk\nM220 S100 ;Reset Feedrate\nM221 S100 ;Reset Flowrate\n\nG28 ;Home\n\nG92 E0 ;Reset Extruder\nG1 Z2.0 F3000 ;Move Z Axis up\nG1 X10.1 Y20 Z0.28 F5000.0 ;Move to start position\nG1 X10.1 Y200.0 Z0.28 F1500.0 E15 ;Draw the first line\nG1 X10.4 Y200.0 Z0.28 F5000.0 ;Move to side a little\nG1 X10.4 Y20 Z0.28 F1500.0 E30 ;Draw the second line\nG92 E0 ;Reset Extruder\nG1 Z2.0 F3000 ;Move Z Axis up\n"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "2 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": "True"}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 10}, "retraction_hop": {"value": 0.2}, "retraction_min_travel": {"value": 1.5}, "retraction_prime_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_retract_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "skin_overlap": {"value": 10.0}, "skirt_brim_speed": {"value": "speed_layer_0"}, "skirt_gap": {"value": 10.0}, "skirt_line_count": {"value": 3}, "speed_layer_0": {"value": 20.0}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_print": {"value": 50.0}, "speed_roofing": {"value": "speed_topbottom"}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_travel": {"value": "150.0 if speed_print < 60 else 250.0 if speed_print > 100 else speed_print * 2.5"}, "speed_travel_layer_0": {"value": "100 if speed_layer_0 < 20 else 150 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"value": 5}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width / 2.0 / layer_height)))"}, "support_brim_enable": {"value": true}, "support_brim_width": {"value": 4}, "support_infill_rate": {"value": "0 if support_structure == 'tree' else 20"}, "support_interface_density": {"value": 75}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"value": "'grid'"}, "support_pattern": {"value": "'zigzag'"}, "support_wall_count": {"value": 1}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height * 2"}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 3"}, "travel_avoid_other_parts": {"value": true}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0.0}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_corner": {"value": "'z_seam_corner_inner'"}, "z_seam_type": {"value": "'sharpest_corner'"}}}