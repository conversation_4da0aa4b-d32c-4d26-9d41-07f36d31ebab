{"version": 2, "name": "Ultimaker 2 Go", "inherits": "ultimaker2", "metadata": {"author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Ultimaker B.V.", "file_formats": "text/x-gcode", "platform": "ultimaker2go_platform.obj", "firmware_file": "MarlinUltimaker2go.hex", "machine_extruder_trains": {"0": "ultimaker2_go_extruder_0"}, "platform_offset": [0, 0, 0], "platform_texture": "Ultimaker2Gobackplate.png", "quality_definition": "ultimaker2", "weight": 3}, "overrides": {"machine_depth": {"default_value": 120}, "machine_disallowed_areas": {"default_value": [[[-60, 60], [-33, 60], [-35, 52], [-60, 52]], [[60, 60], [60, 52], [35, 52], [33, 60]], [[-60, -60], [-60, -52], [-35, -52], [-33, -60]], [[60, -60], [33, -60], [35, -52], [60, -52]]]}, "machine_heated_bed": {"default_value": false}, "machine_height": {"default_value": 115}, "machine_name": {"default_value": "Ultimaker 2 Go"}, "machine_width": {"default_value": 120}}}