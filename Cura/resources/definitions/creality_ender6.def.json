{"version": 2, "name": "Creal<PERSON> Ender-6", "inherits": "creality_base", "metadata": {"visible": true, "quality_definition": "creality_base"}, "overrides": {"gantry_height": {"value": 25}, "machine_depth": {"default_value": 260}, "machine_end_gcode": {"default_value": "G91 ;Relative positioning\nG1 E-2 F2700 ;Retract a bit\nG1 E-2 Z0.2 F2400 ;Retract and raise Z\nG1 X5 Y5 F3000 ;Wipe out\nG1 Z10 ;Raise Z more\nG90 ;Absolute positioning\n\nG28 X Y ;Present print\nM106 S0 ;Turn-off fan\nM104 S0 ;Turn-off hotend\nM140 S0 ;Turn-off bed\n\nM84 X Y E ;Disable all steppers but Z\n"}, "machine_head_with_fans_polygon": {"default_value": [[-26, 34], [-26, -32], [32, -32], [32, 34]]}, "machine_height": {"default_value": 400}, "machine_name": {"default_value": "Creal<PERSON> Ender-6"}, "machine_start_gcode": {"default_value": "\nG28 ;Home\n\nG92 E0 ;Reset Extruder\nG1 Z2.0 F3000 ;Move Z Axis up\nG1 X10.1 Y20 Z0.28 F5000.0 ;Move to start position\nG1 X10.1 Y200.0 Z0.28 F1500.0 E15 ;Draw the first line\nG1 X10.4 Y200.0 Z0.28 F5000.0 ;Move to side a little\nG1 X10.4 Y20 Z0.28 F1500.0 E30 ;Draw the second line\nG92 E0 ;Reset Extruder\nG1 Z2.0 F3000 ;Move Z Axis up\n"}, "machine_width": {"default_value": 260}, "speed_print": {"value": 80.0}}}