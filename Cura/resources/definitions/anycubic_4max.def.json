{"version": 2, "name": "Anycubic 4Max", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON>", "manufacturer": "Anycubic", "file_formats": "text/x-gcode", "platform": "anycubic_4max_platform.3mf", "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "anycubic_4max_extruder_0"}, "preferred_quality_type": "normal", "quality_definition": "anycubic_4max"}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_layer_0": {"value": "acceleration_topbottom"}, "acceleration_prime_tower": {"value": "math.ceil(acceleration_print * 2000 / 4000)"}, "acceleration_print": {"value": "900"}, "acceleration_support": {"value": "math.ceil(acceleration_print * 2000 / 4000)"}, "acceleration_support_interface": {"value": "acceleration_topbottom"}, "acceleration_topbottom": {"value": "math.ceil(acceleration_print * 1000 / 3000)"}, "acceleration_travel": {"value": "acceleration_print"}, "acceleration_wall": {"value": "math.ceil(acceleration_print * 1000 / 3000)"}, "acceleration_wall_0": {"value": "math.ceil(acceleration_wall * 1000 / 1000)"}, "adhesion_type": {"default_value": "skirt"}, "gantry_height": {"value": "25.0"}, "infill_before_walls": {"value": false}, "infill_pattern": {"value": "'zigzag'"}, "jerk_enabled": {"value": "True"}, "jerk_layer_0": {"value": "jerk_topbottom"}, "jerk_prime_tower": {"value": "math.ceil(jerk_print * 15 / 25)"}, "jerk_print": {"value": "11"}, "jerk_support": {"value": "math.ceil(jerk_print * 15 / 25)"}, "jerk_support_interface": {"value": "jerk_topbottom"}, "jerk_topbottom": {"value": "math.ceil(jerk_print * 5 / 25)"}, "jerk_wall": {"value": "math.ceil(jerk_print * 10 / 25)"}, "jerk_wall_0": {"value": "math.ceil(jerk_wall * 5 / 10)"}, "machine_acceleration": {"default_value": 1500}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 220}, "machine_end_gcode": {"default_value": "M104 S0 ; turn off extruder\nM140 S0 ; turn off bed\nM84 ; disable motors\nM107\nG91 ;relative positioning\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 ;X-20 Y-20 F{speed_travel} ;move Z up a bit and retract filament even more\nG28 X0 ;Y0 ;move X/Y to min endstops, so the head is out of the way\nG1 Y180 F2000\nM84 ;steppers off\nG90\nM300 P300 S4000"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 300}, "machine_max_acceleration_x": {"default_value": 1500}, "machine_max_acceleration_y": {"default_value": 1500}, "machine_max_acceleration_z": {"default_value": 100}, "machine_max_feedrate_x": {"default_value": 300}, "machine_max_feedrate_y": {"default_value": 300}, "machine_max_feedrate_z": {"default_value": 10}, "machine_max_jerk_e": {"default_value": 11.0}, "machine_max_jerk_xy": {"default_value": 11.0}, "machine_max_jerk_z": {"default_value": 0.4}, "machine_name": {"default_value": "Anycubic 4Max"}, "machine_start_gcode": {"default_value": "G21 ;metric values\nG90 ;absolute positioning\nM82 ;set extruder to absolute mode\nM107 ;start with the fan off\nG28 X0 Y0 ;move X/Y to min endstops\nG28 Z0 ;move Z to min endstops\nG1 Z15.0 F{speed_travel} ;move the platform down 15mm\nG92 E0 ;zero the extruded length\nG1 F200 E3 ;extrude 3mm of feed stock\nG92 E0 ;zero the extruded length again\nG1 F{speed_travel}\nM117 Printing...\nG5"}, "machine_width": {"default_value": 220}, "material_bed_temperature": {"maximum_value": "150"}, "material_bed_temperature_layer_0": {"maximum_value": "150"}, "skin_overlap": {"value": "10"}, "speed_layer_0": {"value": "20"}, "speed_print": {"value": "40"}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "math.ceil(speed_print * 20 / 35)"}, "speed_travel": {"value": "60"}, "speed_wall": {"value": "math.ceil(speed_print * 30 / 35)"}, "speed_wall_0": {"value": "math.ceil(speed_wall * 20 / 30)"}, "speed_wall_x": {"value": "speed_wall"}}}