{"version": 2, "name": "Trimaker Cosmos II", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "Trimaker", "manufacturer": "Trimaker", "file_formats": "text/x-gcode", "platform": "trimaker_cosmosII_platform.stl", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "generic_abs", "generic_cpe", "generic_hips", "generic_nylon", "generic_pc", "generic_petg", "generic_pla", "generic_pva", "generic_tpu", "imade3d_petg", "imade3d_pla", "innofill_innoflex60", "verbatim_bvoh"], "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "trimaker_cosmosII_extruder"}, "platform_offset": [-110.5, -28.3, 134], "preferred_material": "redd_pla", "preferred_quality_type": "normal"}, "overrides": {"adhesion_type": {"default_value": "skirt"}, "cool_fan_enabled": {"default_value": true}, "cool_fan_speed": {"value": "100.0 if cool_fan_enabled else 0.0"}, "default_material_bed_temperature": {"default_value": 60}, "default_material_print_temperature": {"default_value": 200}, "gantry_height": {"value": 2}, "infill_pattern": {"value": "'grid'"}, "infill_sparse_density": {"default_value": 25}, "infill_sparse_thickness": {"value": "resolveOrValue('layer_height')"}, "layer_height": {"default_value": 0.2}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 200}, "machine_end_gcode": {"default_value": ";CODIGO FINAL\n M107; Fan off\n G90; Set to absolute positioning\n G1 X0 Y0 Z201; Get extruder out of way\n G92 E0; Reset extruder position\n G1 E-1; Reduce filament pressure\n G92 E0; Reset extruder position again\n M140 S0; Disable heated bed\n M104 S0; Disable extruder\n M84; Turn steppers off"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 200}, "machine_name": {"default_value": "Trimaker Cosmos II"}, "machine_start_gcode": {"default_value": ";Start GCode - Cosmos II - 3.x.x_SEGcTK_1.1\n M104 S120; <PERSON><PERSON><PERSON><PERSON> a calentar extrusor\n G21; Unidades en mm\n G90; absolute positioning\n M82; set extruder to absolute mode\n M107; Apagar FAN\n G28; Home\n M190 S{material_bed_temperature_layer_0}\n G29; Senso la cama\n M500\n G1 F5000 X0.5 Y0.5\n M109 S{material_print_temperature}\n M900 K0.04\n G1 F200 Z10\n G92 E0; Defino cero en la posición del actual del extrusor\n G1 F200 X0.5 Y0.5 Z0.300; Posiciono antes de hacer una línea\n G1 F900 X0.5 Y51.5 E2.56436; Hago una línea\n "}, "machine_width": {"default_value": 200}, "material_diameter": {"default_value": 1.75}, "material_flow": {"value": 100}, "retraction_amount": {"default_value": 1}, "retraction_enable": {"default_value": true}, "retraction_speed": {"default_value": 45}, "speed_print": {"default_value": 45}, "speed_travel": {"value": "speed_print if magic_spiralize else 100"}, "speed_wall_0": {"value": 35}, "speed_wall_x": {"value": 45}, "support_angle": {"default_value": 50}, "support_enable": {"default_value": true}, "support_pattern": {"default_value": "zigzag"}, "support_type": {"default_value": "everywhere"}, "support_xy_distance": {"default_value": 0.7}, "support_z_distance": {"default_value": 0.17}, "top_bottom_thickness": {"value": "layer_height * 6"}, "wall_thickness": {"value": "line_width * 3"}, "xy_offset": {"default_value": 0}, "xy_offset_layer_0": {"value": -0.1}}}