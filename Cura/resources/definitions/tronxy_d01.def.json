{"version": 2, "name": "Tronxy D01", "inherits": "tronxy_x", "metadata": {"visible": true, "platform": "tronxy.stl", "quality_definition": "tronxy_x"}, "overrides": {"gantry_height": {"value": 30}, "machine_acceleration": {"value": 120}, "machine_depth": {"default_value": 220}, "machine_end_gcode": {"default_value": "M83 ; Set extrder to Relative\nG1 E-5 F3000 ; Retract 5mm of filament at 50mm/s\nG90 ; Set all axis to Absolute \nG1 X0 Y{machine_depth} ; Park print head\nG1 Z10 ; Move up 10mm\nM106 S0 ; Set fan speed to 0\nM104 S0 ; Set bed temp to 0\nM140 S0 ; Set Nozzle temp to 0\nM84 ; Disable all stepper motors\n"}, "machine_head_with_fans_polygon": {"default_value": [[-32, 45], [-32, -30], [32, -30], [32, 45]]}, "machine_height": {"default_value": 220}, "machine_name": {"default_value": "Tronxy D01"}, "machine_start_gcode": {"default_value": "; D01 Start Code\nG21\nG90\nM82\nM107 T0\nM140 S{material_bed_temperature_layer_0}\nM104 S{material_print_temperature_layer_0} T0\nM190 S{material_bed_temperature_layer_0}\nM109 S{material_print_temperature_layer_0} T0\nG28\nG92 E0\nG1 Z2.0 F3000 ; Move Z Axis up little to preventscratching of Heat Bed\nG1 X1 Y20 Z0.3 F3600.0 ; Move to start position\nG1 X1 Y200.0 Z0.3 F1500.0 E25 ; Draw the first line\nG1 X1.6 Y200.0 Z0.3 F3600.0 ; Move to side a little\nG1 X1.6 Y20 Z0.3 F1500.0 E50 ; Draw the second line\nG92 E0 ; Reset Extruder\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X5 Y20 Z0.3 F3600.0 ; Move over to prevent blob squish"}, "machine_width": {"default_value": 220}}}