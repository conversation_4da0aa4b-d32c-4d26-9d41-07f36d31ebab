{"version": 2, "name": "Flsun V400", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "FLSUN, G<PERSON><PERSON><PERSON> Cyril", "manufacturer": "Flsun", "file_formats": "text/x-gcode", "platform": "flsun_v400.stl", "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "flsun_v400_extruder_0"}, "platform_offset": [0, -40.6455, -43.5]}, "overrides": {"gantry_height": {"value": "0"}, "infill_sparse_density": {"default_value": 15}, "machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 300}, "machine_end_gcode": {"default_value": "M107 T0\nM104 S0\nM104 S0 T1\nM140 S0\nG92 E0\nG91\nG1 E-1 F300\nG1 Z+0.5  F6000\nG28 \nG90 ;absolute positioning\n"}, "machine_head_with_fans_polygon": {"default_value": [[0, 0], [0, 0], [0, 0], [0, 0]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 410}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": "G21\nG90\nM82\nM107 T0\nM140 S{material_bed_temperature}\nM104 S{material_print_temperature} T0\nM190 S{material_bed_temperature}\nM109 S{material_print_temperature} T0\nG28\nG1 F3000 Z1\nG1 X-150 Y0 Z0.4\nG92 E0\nG3 X0 Y-130 I150 Z0.3 E30 F2000\nG92 E0\n"}, "machine_width": {"default_value": 300}, "material_diameter": {"default_value": 1.75}, "z_seam_type": {"value": "'back'"}}}