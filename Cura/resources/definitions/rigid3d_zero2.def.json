{"version": 2, "name": "Rigid3D Zero2", "inherits": "rigid3d_base", "metadata": {"visible": true, "platform": "rigid3d_zero2_platform.3mf", "preferred_quality_type": "standard", "quality_definition": "rigid3d_base"}, "overrides": {"gantry_height": {"value": 25}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 200}, "machine_end_gcode": {"default_value": "; -- END GCODE --\nG1 X0 Y180 ; Konuma git\nM107 ; <PERSON><PERSON> kapat\nG91 ; <PERSON><PERSON><PERSON> konumlama\nG0 Z20 ; Tablayi alcalt\nT0\nG1 E-2 ; Filaman basincini dusur\nM104 T0 S0 ; Ekstruder isiticiyi kapat\nG90 ; Mutlak konumlama\nG92 E0 ; Ekstruder konumu sifirla\nM140 S0 ; Tabla isiticiyi kapat\nM84 ; Motorlari durdur\nM300 S2093 P150 ; Baski sonu melodisi\nM300 S2637 P150\nM300 S3135 P150\nM300 S4186 P150\nM300 S3135 P150\nM300 S2637 P150\nM300 S2793 P150\nM300 S2349 P150\nM300 S1975 P150\nM300 S2093 P450\n; -- end of END GCODE --\n"}, "machine_head_with_fans_polygon": {"default_value": [[-30, 65], [-30, -30], [30, -30], [30, 65]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 192}, "machine_name": {"default_value": "Rigid3D Zero2"}, "machine_start_gcode": {"default_value": "; -- START GCODE --\nG21 ; mm olculer\nG28 ; Eks<PERSON><PERSON>i sifirla\nM420 S1 ; <PERSON><PERSON><PERSON> tabla seviyeleme\nM107 ; <PERSON><PERSON> kapat\nG90 ; Mutlak konumlama\nG1 Z5 F180 ; Z eksenini 5mm yukselt\nG1 X30 Y30 F3000 ; Konuma git\nM82 ; Ekstruder mutlak mod\nG92 E0 ; Ekstruder konumu sifirla\n; -- end of START GCODE --"}, "machine_width": {"default_value": 200}}}