{"version": 2, "name": "WEEDO X40 V3+", "inherits": "weedo_base", "metadata": {"visible": true, "author": "WEEDO", "manufacturer": "WEEDO", "file_formats": "text/x-gcode", "machine_extruder_trains": {"0": "weedo_x40v3_extruder_left", "1": "weedo_x40v3_extruder_right"}, "platform_offset": [0, 0, 0]}, "overrides": {"adhesion_type": {"default_value": "brim"}, "machine_always_write_active_tool": {"default_value": true}, "machine_depth": {"default_value": 300}, "machine_end_gcode": {"default_value": ";(*********end X40 End.gcode*******)\nM203 Z15\nM605 S1 ;Set to auto-park\nG28 X F3000\nG91 ; Relative positioning\nG1 E-1 ; Reduce filament pressure\nG90\nG1 Y300 F3000\nM104 S0 T0\nM104 S0 T1\nM140 S0\nM107 P0\nM107 P1\nM82"}, "machine_extruder_count": {"default_value": 2}, "machine_height": {"default_value": 400}, "machine_name": {"default_value": "WEEDO X40"}, "machine_nozzle_heat_up_speed": {"default_value": 1.7}, "machine_start_gcode": {"default_value": ";MachineType:{machine_name}\n;FilamentType:{material_type}\n;InfillDensity:{infill_sparse_density}\n;BedTemperature:{material_bed_temperature}\n\n;(**** start.gcode for  X40 ****)\nM203 Z15\nM140 S{material_bed_temperature_layer_0}\nM104 S{material_print_temperature_layer_0} T{initial_extruder_nr}\nG28 ;This command will use tool0 to get the x endstop and setup coordinate system\nG29 ;Auto level\nM107 P0 ;Turn off fan\nM107 P1\nG90 ; Absolute positioning\nM82 ; Extruder in absolute mode\nG1 X-47 F3000\nG1 Z20 F600\nT{initial_extruder_nr} S ;Set start extruder nr after G28\nM109 S{material_print_temperature_layer_0} T{initial_extruder_nr}\nG92 E0\nG1 E22 F300\nG92 E0\nM203 Z2"}, "machine_width": {"default_value": 300}, "material_print_temp_prepend": {"default_value": false}, "material_standby_temperature": {"value": "max(0, material_print_temperature - 20)"}, "ooze_shield_angle": {"default_value": 0}, "ooze_shield_dist": {"default_value": 3.0}, "ooze_shield_enabled": {"default_value": true}, "retraction_amount": {"default_value": 4}, "speed_print": {"value": 60.0}, "speed_support": {"value": "round(speed_print * 0.96, 1)"}, "speed_topbottom": {"value": "round(speed_print * 0.58, 1)"}, "speed_wall": {"value": "speed_print / 2"}, "switch_extruder_retraction_amount": {"value": 0}}}