{"version": 2, "name": "WEEDO Base", "inherits": "fdmprinter", "metadata": {"visible": false, "author": "WEEDO", "manufacturer": "WEEDO", "file_formats": "text/x-gcode", "exclude_materials": ["3D-Fuel_PLA_PRO_Black", "3D-Fuel_PLA_SnapSupport", "bestfilament_abs_skyblue", "bestfilament_petg_orange", "bestfilament_pla_green", "leapfrog_abs_natural", "leapfrog_epla_natural", "leapfrog_pva_natural", "generic_pc_175", "goofoo_abs", "goofoo_asa", "goofoo_bronze_pla", "goofoo_emarble_pla", "goofoo_esilk_pla", "goofoo_hips", "goofoo_pa_cf", "goofoo_pa", "goofoo_pc", "goofoo_peek", "goofoo_petg", "goofoo_pla", "goofoo_pva", "goofoo_tpe_83a", "goofoo_tpu_87a", "goofoo_tpu_95a", "goofoo_wood_pla", "emotiontech_abs", "emotiontech_absx", "emotiontech_acetate", "emotiontech_asax", "emotiontech_bvoh", "emotiontech_copa", "emotiontech_hips", "emotiontech_nylon_1030", "emotiontech_nylon_1030cf", "emotiontech_nylon_1070", "emotiontech_pc", "emotiontech_pekk", "emotiontech_petg", "emotiontech_pla", "emotiontech_pla_hr_870", "emotiontech_pva-m", "emotiontech_pva-s", "emotiontech_tpu98a", "eryone_petg", "eryone_pla_glow", "eryone_pla_matte", "eryone_pla_wood", "eryone_pla", "eryone_tpu", "eSUN_PETG_Black", "eSUN_PETG_Grey", "eSUN_PETG_Purple", "eSUN_PLA_PRO_Black", "eSUN_PLA_PRO_Grey", "eSUN_PLA_PRO_Purple", "eSUN_PLA_PRO_White", "Extrudr_GreenTECPro_Anthracite_175", "Extrudr_GreenTECPro_Black_175", "Extrudr_GreenTECPro_Blue_175", "Extrudr_GreenTECPro_Nature_175", "Extrudr_GreenTECPro_Red_175", "Extrudr_GreenTECPro_Silver_175", "Extrudr_GreenTECPro_White_175", "verbatim_bvoh_175", "Vertex_Delta_ABS", "Vertex_Delta_PET", "Vertex_Delta_PLA", "Vertex_Delta_TPU", "chromatik_pla", "dsm_arnitel2045_175", "dsm_novamid1070_175", "fabtotum_abs", "fabtotum_nylon", "fabtotum_pla", "fabtotum_tpu", "fdplast_abs_tomato", "fdplast_petg_gray", "fdplast_pla_olive", "fiberlogy_hd_pla", "filo3d_pla", "filo3d_pla_green", "filo3d_pla_red", "imade3d_petg_green", "imade3d_petg_pink", "imade3d_pla_green", "imade3d_pla_pink", "imade3d_petg_175", "imade3d_pla_175", "innofill_innoflex60_175", "layer_one_black_pla", "layer_one_dark_gray_pla", "layer_one_white_pla", "octofiber_pla", "polyflex_pla", "polymax_pla", "polyplus_pla", "polywood_pla", "redd_abs", "redd_asa", "redd_hips", "redd_nylon", "redd_petg", "redd_pla", "redd_tpe", "tizyx_abs", "tizyx_flex", "tizyx_petg", "tizyx_pla_bois", "tizyx_pla", "tizyx_pva", "Vertex_Delta_ABS", "Vertex_Delta_PET", "Vertex_Delta_PLA_Glitter", "Vertex_Delta_PLA_Mat", "Vertex_Delta_PLA_Satin", "Vertex_Delta_PLA_Wood", "Vertex_Delta_PLA", "Vertex_Delta_TPU", "volumic_abs_ultra", "volumic_arma_ultra", "volumic_asa_ultra", "volumic_br80_ultra", "volumic_bumper_ultra", "volumic_cu80_ultra", "volumic_flex93_ultra", "volumic_medical_ultra", "volumic_nylon_ultra", "volumic_pekk_carbone", "volumic_petg_ultra", "volumic_petgcarbone_ultra", "volumic_pla_ultra", "volumic_pp_ultra", "volumic_strong_ultra", "volumic_support_ultra", "xyzprinting_abs", "xyzprinting_antibact_pla", "xyzprinting_carbon_fiber", "xyzprinting_colorinkjet_pla", "xyzprinting_flexible", "xyzprinting_metallic_pla", "xyzprinting_nylon", "xyzprinting_petg", "xyzprinting_pla", "xyzprinting_tough_pla", "xyzprinting_tpu", "zyyx_pro_flex", "zyyx_pro_pla"], "has_machine_quality": false, "has_materials": true, "has_variants": false, "machine_extruder_trains": {"0": "weedo_base_extruder_0"}, "preferred_material": "generic_pla_175", "preferred_quality_type": "draft"}, "overrides": {"adhesion_type": {"default_value": "raft"}, "draft_shield_dist": {"default_value": 3}, "infill_before_walls": {"default_value": false}, "infill_line_width": {"value": "line_width * 1.25"}, "infill_material_flow": {"value": "max(0, material_flow - 5)"}, "infill_pattern": {"value": "'zigzag'"}, "infill_sparse_density": {"default_value": 10.0}, "initial_layer_line_width_factor": {"default_value": 110.0}, "layer_0_z_overlap": {"value": 0.09}, "machine_acceleration": {"default_value": 3000}, "machine_end_gcode": {"default_value": "G92 E0\nG1 E-3 F1680 \nG28 Z F400; Get extruder out of way.\nM107 ; Turn off fan\n; Disable all extruder\nM104 T0 S0\nG90 ; Absolute positioning\nG92 E0 ; Reset extruder position\nM84 ; Turn steppers off\n"}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"default_value": 1600}, "machine_max_acceleration_x": {"default_value": 3000}, "machine_max_acceleration_y": {"default_value": 3000}, "machine_max_acceleration_z": {"default_value": 120}, "machine_max_feedrate_e": {"default_value": 50}, "machine_max_feedrate_x": {"default_value": 200}, "machine_max_feedrate_y": {"default_value": 130}, "machine_max_feedrate_z": {"default_value": 10}, "machine_max_jerk_e": {"default_value": 5.1}, "machine_max_jerk_xy": {"default_value": 10.0}, "machine_max_jerk_z": {"default_value": 5}, "machine_min_cool_heat_time_window": {"default_value": 1200.0}, "machine_name": {"default_value": "WEEDO Base"}, "machine_nozzle_cool_down_speed": {"default_value": 0.67}, "machine_nozzle_heat_up_speed": {"default_value": 1.8}, "machine_nozzle_tip_outer_diameter": {"default_value": 0.8}, "machine_start_gcode": {"default_value": "G28 ;Home\nG92 E0\nG1 F200 E3\nG92 E0"}, "machine_steps_per_mm_e": {"default_value": 96}, "machine_steps_per_mm_x": {"default_value": 94}, "machine_steps_per_mm_y": {"default_value": 94}, "machine_steps_per_mm_z": {"default_value": 400}, "material_bed_temp_wait": {"default_value": false}, "material_bed_temperature": {"maximum_value_warning": "96"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_flow": {"default_value": 95.0}, "material_flow_layer_0": {"default_value": 95.0}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "material_print_temp_wait": {"default_value": false}, "material_print_temperature": {"maximum_value": "300"}, "material_print_temperature_layer_0": {"value": "min(material_print_temperature + 10, 300)"}, "material_standby_temperature": {"default_value": 175.0}, "prime_tower_min_volume": {"default_value": 10.0}, "prime_tower_size": {"default_value": 15.0}, "raft_airgap": {"default_value": 0.19}, "raft_base_fan_speed": {"value": 0.0}, "raft_base_speed": {"value": 20.0}, "raft_base_thickness": {"value": 0.3}, "raft_interface_speed": {"value": 33.0}, "raft_margin": {"default_value": 8.0}, "raft_surface_speed": {"value": 40.0}, "raft_surface_thickness": {"value": 0.25}, "retraction_amount": {"default_value": 3}, "retraction_combing": {"value": "'off'"}, "retraction_extrusion_window": {"value": 1.0}, "retraction_hop_after_extruder_switch": {"default_value": false}, "retraction_min_travel": {"value": 0.8}, "retraction_speed": {"default_value": 28.0}, "skin_outline_count": {"value": "0"}, "skirt_brim_speed": {"value": 26.0}, "skirt_line_count": {"default_value": 2}, "speed_layer_0": {"value": 26.0}, "speed_prime_tower": {"value": "speed_support"}, "speed_print": {"default_value": 70.0}, "speed_support": {"value": "round(speed_print * 0.82, 1)"}, "speed_support_interface": {"value": "round(speed_support * 0.689, 1)"}, "speed_topbottom": {"value": "round(speed_print * 0.65, 1)"}, "speed_travel": {"value": 105.0}, "speed_travel_layer_0": {"value": 80.0}, "speed_wall": {"value": "max(5, round(speed_print / 2 - 5, 1))"}, "speed_wall_0": {"value": "max(5, speed_wall - 5)"}, "speed_wall_x": {"value": "speed_wall"}, "support_angle": {"default_value": 60.0}, "support_connect_zigzags": {"default_value": false}, "support_interface_density": {"default_value": 60.0}, "support_interface_enable": {"default_value": true}, "support_interface_height": {"default_value": 0.8}, "support_interface_pattern": {"default_value": "lines"}, "support_material_flow": {"value": "max(0, material_flow - 5)"}, "support_z_distance": {"default_value": 0.18}, "switch_extruder_retraction_amount": {"value": 16.5}, "switch_extruder_retraction_speeds": {"default_value": 28.0}, "top_skin_preshrink": {"value": 0.0}, "wall_0_wipe_dist": {"value": 0.0}, "z_seam_corner": {"default_value": "z_seam_corner_any"}}}