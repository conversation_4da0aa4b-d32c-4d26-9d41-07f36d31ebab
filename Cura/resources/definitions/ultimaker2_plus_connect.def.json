{"version": 2, "name": "Ultimaker 2+ Connect", "inherits": "ultimaker2", "metadata": {"author": "<PERSON><PERSON><PERSON><PERSON>", "manufacturer": "Ultimaker B.V.", "file_formats": "application/x-ufp;text/x-gcode", "platform": "ultimaker2_plus_connect_platform.obj", "exclude_materials": ["generic_bam", "generic_cffcpe", "generic_cffpa", "generic_flexible", "generic_gffcpe", "generic_gffpa", "generic_hips", "generic_petcf", "generic_pva", "structur3d_", "ultimaker_bam", "ultimaker_petcf", "ultimaker_pva"], "first_start_actions": [], "has_machine_materials": true, "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "ultimaker2_plus_connect_extruder_0"}, "platform_offset": [-1, -6, 0], "platform_texture": "Ultimaker2PlusConnectbackplate.png", "preferred_variant_name": "0.4 mm", "supported_actions": [], "supports_network_connection": true, "supports_usb_connection": false, "weight": 1}, "overrides": {"gantry_height": {"value": "52"}, "infill_overlap": {"value": "0"}, "infill_pattern": {"value": "'zigzag' if infill_sparse_density > 80 else 'grid'"}, "infill_wipe_dist": {"value": "0"}, "layer_height_0": {"value": "round(machine_nozzle_size / 1.5, 2)"}, "machine_depth": {"default_value": 220}, "machine_disallowed_areas": {"default_value": [[[-115, 112.5], [-83, 112.5], [-85, 104.0], [-115, 104.0]], [[115, 112.5], [115, 104.0], [104, 104.0], [102, 112.5]], [[-115, -112.5], [-115, -104.0], [-87, -104.0], [-85, -112.5]], [[115, -112.5], [104, -112.5], [106, -104.0], [115, -104.0]]]}, "machine_end_gcode": {"value": "''"}, "machine_gcode_flavor": {"default_value": "<PERSON>"}, "machine_head_with_fans_polygon": {"default_value": [[-44, 14], [-44, -34], [64, 14], [64, -34]]}, "machine_heat_zone_length": {"default_value": 20}, "machine_height": {"default_value": 205}, "machine_name": {"default_value": "Ultimaker 2+ Connect"}, "machine_show_variants": {"default_value": true}, "machine_start_gcode": {"value": "''"}, "machine_width": {"default_value": 223}, "material_initial_print_temperature": {"maximum_value": 260, "value": "material_print_temperature"}, "material_print_temperature": {"maximum_value": 260}, "material_print_temperature_layer_0": {"maximum_value": 260}, "meshfix_maximum_deviation": {"value": "(layer_height / 3) if magic_spiralize else (layer_height / 4)"}, "meshfix_maximum_resolution": {"value": "(speed_wall_0 + speed_wall_x) / 60"}, "meshfix_maximum_travel_resolution": {"value": 0.5}, "optimize_wall_printing_order": {"value": "True"}, "prime_blob_enable": {"default_value": true, "enabled": true, "value": "resolveOrValue('print_sequence') != 'one_at_a_time'"}, "retraction_prime_speed": {"value": "15"}, "retraction_speed": {"value": "45"}, "speed_support": {"value": "speed_wall_0"}, "speed_wall_x": {"value": "speed_wall"}, "zig_zaggify_infill": {"value": "gradual_infill_steps == 0"}}}