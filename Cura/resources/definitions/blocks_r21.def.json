{"version": 2, "name": "Blocks R21", "inherits": "blocks_base", "metadata": {"visible": true, "platform": "blocks_r21_platform.stl", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "blocks_r21_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal", "preferred_variant_name": "ST - 0.4mm", "quality_definition": "blocks_base", "variants_name": "Print Core"}, "overrides": {"machine_depth": {"default_value": 210}, "machine_end_gcode": {"default_value": "M104 S0 ;extruder heater off\nM140 S0 ;heated bed heater off (if you have it)\nG91 ;relative positioning\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 X-20 Y-20 F6000 ;move Z up a bit and retract filament even more\nG28 X0;move X/Y to min endstops, so the head is out of the way\nG1 Y210 F5000.0\nG1 Z+70\nM84 ;steppers off\nG90 ;absolute positioning\n"}, "machine_height": {"default_value": 250}, "machine_name": {"default_value": "Blocks R21"}, "machine_start_gcode": {"default_value": "G21\nG90 ;absolute positioning\nM82 ;set extruder to absolute mode\nM107 ;start with the fan off\nG28 X0 Y0  ;move X/Y to min endstops\nG28 Z0 ;move Z to min endstops\nG29\nM117 ; Purge extruder\nG92 E0 ; Reset Extruder\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X0.1 Y20 Z0.3 F5000.0 ; Move to start position\nG1 X0.1 Y200.0 Z0.3 F1500.0 E15 ; Draw the first line\nG1 X0.4 Y200.0 Z0.3 F5000.0 ; Move to side a little\nG1 X0.4 Y20 Z0.3 F1500.0 E30 ; Draw the second line\nG92 E0 ; Reset Extruder\nG1 Z1.0 F3000\nG1 X0.1 Y15 F3000.0\nG1 Z0.1 F3000.0\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nM117 Printing...\n"}, "machine_width": {"default_value": 300}, "retraction_retract_speed": {"value": 50}}}