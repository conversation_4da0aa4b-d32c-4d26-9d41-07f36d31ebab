{"version": 2, "name": "LNL3D Printer", "inherits": "fdmprinter", "metadata": {"author": "LNL3D", "manufacturer": "LNL3D", "file_formats": "text/x-gcode", "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "lnl3d_extruder_left", "1": "lnl3d_extruder_right"}, "preferred_material": "generic_pla", "preferred_quality_type": "standard", "preferred_variant_name": "0.4mm Nozzle", "quality_definition": "lnl3d_base", "variants_name": "Nozzle Size"}, "overrides": {"acceleration_print": {"value": 500}, "acceleration_roofing": {"enabled": "acceleration_enabled and roofing_layer_count > 0 and top_layers > 0"}, "acceleration_travel": {"value": 500}, "acceleration_travel_layer_0": {"value": "acceleration_travel"}, "adaptive_layer_height_variation": {"value": 0.04}, "adaptive_layer_height_variation_step": {"value": 0.04}, "adhesion_type": {"value": "'skirt'"}, "brim_width": {"value": 4}, "cool_fan_full_at_height": {"value": "layer_height_0 + 2 * layer_height"}, "cool_min_layer_time": {"value": 10}, "fill_outline_gaps": {"value": false}, "infill_overlap": {"value": 30.0}, "infill_pattern": {"value": "'lines'"}, "infill_wipe_dist": {"value": 0.0}, "initial_layer_line_width_factor": {"default_value": 130.0}, "jerk_print": {"value": 8}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "layer_height_0": {"default_value": 0.2}, "machine_acceleration": {"value": 500}, "machine_end_gcode": {"default_value": "M104 T0 S0                   ;left extruder heater off\nM104 T1 S0                   ;right extruder heater off\nM140 S0                      ;heated bed heater off (if you have it)\nG91                          ;relative positioning\nG1 E-1 F300                  ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 X-20 Y-20 F9000 ;move Z up a bit and retract filament even more\nG28 X0 Y0                    ;move X/Y to min endstops, so the head is out of the way\nM84                          ;steppers off\nG90                          ;absolute positioning"}, "machine_extruder_count": {"default_value": 2}, "machine_heated_bed": {"default_value": true}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 500}, "machine_max_acceleration_y": {"value": 500}, "machine_max_acceleration_z": {"value": 100}, "machine_max_feedrate_e": {"value": 80}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 10}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.4}, "machine_start_gcode": {"default_value": "G21              ;metric values\nG90              ;absolute positioning\nM82              ;set extruder to absolute mode\nM107             ;start with the fan off\nG28              ;move to min endstops\nG92 E0           ;reset extruder\nG1 E15 F1500     ;move extruder 15mm\nG1 Z15.0 F3000   ;move the header up 15mm\nM117 printing... ;LCD message"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "meshfix_maximum_resolution": {"value": "0.25"}, "meshfix_maximum_travel_resolution": {"value": "meshfix_maximum_resolution"}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "2 if support_structure == 'normal' else 0"}, "optimize_wall_printing_order": {"value": "True"}, "prime_tower_brim_enable": {"value": true}, "prime_tower_wipe_enabled": {"default_value": false}, "raft_airgap": {"default_value": 0.2}, "raft_margin": {"default_value": 2}, "retraction_combing": {"value": "'off' if retraction_hop_enabled else 'noskin'"}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 4}, "retraction_hop": {"value": 0.2}, "retraction_hop_after_extruder_switch_height": {"value": 0.0}, "retraction_hop_enabled": {"value": "False"}, "retraction_prime_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_retract_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "retraction_speed": {"maximum_value": 200, "maximum_value_warning": "machine_max_feedrate_e if retraction_enable else float('inf')"}, "skin_overlap": {"value": 25}, "skirt_line_count": {"value": 2}, "speed_layer_0": {"value": 20.0}, "speed_prime_tower": {"value": "speed_topbottom"}, "speed_support": {"value": "speed_wall_0"}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_travel": {"value": "80.0 if speed_print < 61 else 120.0 if speed_print > 100 else speed_print * 1.5"}, "speed_travel_layer_0": {"value": "80 if speed_layer_0 < 21 else 100 if speed_layer_0 > 30 else speed_layer_0 * 5"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"value": 5}, "support_angle": {"value": "math.floor(math.degrees(math.atan(line_width/1.4/layer_height)))"}, "support_brim_enable": {"value": true}, "support_brim_width": {"value": 4}, "support_infill_rate": {"value": "0 if support_enable and support_structure == 'tree' else 20"}, "support_interface_density": {"value": 33.333}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "layer_height * 4"}, "support_interface_pattern": {"value": "'grid'"}, "support_pattern": {"value": "'zigzag'"}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_xy_overrides_z": {"value": "'xy_overrides_z'"}, "support_z_distance": {"value": "layer_height if layer_height >= 0.16 else layer_height*2"}, "switch_extruder_retraction_amount": {"value": 8.0}, "switch_extruder_retraction_speeds": {"default_value": 60.0}, "top_bottom_thickness": {"value": "layer_height_0 + layer_height * 4"}, "travel_avoid_other_parts": {"value": false}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}, "wall_0_wipe_dist": {"value": 0.0}, "wall_thickness": {"value": "line_width * 2"}, "z_seam_corner": {"value": "'z_seam_corner_weighted'"}, "z_seam_type": {"value": "'back'"}, "zig_zaggify_infill": {"value": true}}}