{"version": 2, "name": "Flsun QQ", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON>", "manufacturer": "Flsun", "file_formats": "text/x-gcode", "machine_extruder_trains": {"0": "flsun_qq_extruder"}}, "overrides": {"machine_center_is_zero": {"default_value": true}, "machine_depth": {"default_value": 260}, "machine_end_gcode": {"default_value": "M104 S0\nM140 S0\n;Retract the filament\nG92 E1\nG1 E-1 F300\nG28 X0 Y0\nM84"}, "machine_extruder_count": {"default_value": 1}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 285}, "machine_shape": {"default_value": "elliptic"}, "machine_start_gcode": {"default_value": "G28 ;Home\nG1 Z15.0 F6000 ;Move the platform down 15mm\n;Prime the extruder\nG92 E0\nG1 F200 E3\nG92 E0"}, "machine_width": {"default_value": 260}}}