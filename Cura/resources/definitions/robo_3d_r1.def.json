{"version": 2, "name": "Robo 3D R1", "inherits": "fdmprinter", "metadata": {"visible": true, "manufacturer": "Robo 3D", "file_formats": "text/x-gcode", "machine_extruder_trains": {"0": "robo_3d_r1_extruder_0"}, "platform_offset": [0, 0, 0]}, "overrides": {"adhesion_type": {"default_value": "raft"}, "cool_min_layer_time": {"default_value": 7}, "cool_min_speed": {"default_value": 19}, "infill_sparse_density": {"default_value": 10}, "layer_height": {"default_value": 0.2}, "layer_height_0": {"default_value": 0.15}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 245}, "machine_end_gcode": {"default_value": " M104 S0                     ;extruder heater off\n M140 S0                     ;heated bed heater off (if you have it)\n G91                                    ;relative positioning\n G1 E-1 F300                            ;retract the filament a bit before lifting the nozzle, to release some of the pressure\n G1 Z+0.5 E-5 X-20 Y-20 F{speed_travel} ;move Z up a bit and retract filament even more\n G28 X0 Y0                              ;move X/Y to min endstops, so the head is out of the way\n M84                         ;steppers off\n G90                         ;absolute positioning\n"}, "machine_extruder_count": {"default_value": 1}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 210}, "machine_name": {"default_value": "ROBO 3D R1"}, "machine_start_gcode": {"default_value": " G92 E0 ;\n M565 Z-1 ;\n G1 Z5 F5000 ;\n G29 ;\n"}, "machine_width": {"default_value": 225}, "material_flow": {"default_value": 100}, "prime_tower_size": {"default_value": 8.660254037844387}, "raft_airgap": {"default_value": 0.2}, "retraction_amount": {"default_value": 3}, "retraction_combing": {"value": "'off'"}, "retraction_speed": {"default_value": 50}, "speed_print": {"default_value": 40}, "support_angle": {"default_value": 50}, "support_enable": {"default_value": true}, "support_z_distance": {"default_value": 0.22}, "wall_thickness": {"value": "1.2"}}}