{"version": 2, "name": "Anycubic Mega Zero", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "kad", "manufacturer": "Anycubic", "file_formats": "text/x-gcode", "platform": "anycubic_mega_zero_platform.stl", "has_machine_quality": true, "has_materials": true, "machine_extruder_trains": {"0": "anycubic_mega_zero_extruder_0"}, "preferred_material": "generic_pla", "preferred_quality_type": "normal"}, "overrides": {"acceleration_enabled": {"value": false}, "acceleration_print": {"value": 500}, "acceleration_travel": {"value": 500}, "adhesion_type": {"value": "'skirt'"}, "cool_fan_full_at_height": {"value": "layer_height_0"}, "gantry_height": {"value": 25}, "jerk_enabled": {"value": false}, "jerk_print": {"value": 10}, "jerk_travel": {"value": "jerk_print"}, "jerk_travel_layer_0": {"value": "jerk_travel"}, "machine_acceleration": {"value": 500}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 220}, "machine_end_gcode": {"default_value": "M117 Cooling down...\nM104 S0 ; turn off extruder\nM84 ; disable motors\nM107 ; Fan off\nG91 ;relative positioning\nG1 E-1 F300 ;retract the filament a bit before lifting the nozzle, to release some of the pressure\nG1 Z+0.5 E-5 ;X-20 Y-20 F{speed_travel} ;move Z up a bit and retract filament even more\nG28 X0 ;move X to min endstops, so the head is out of the way\nG90 ;Absolute positioning\nG1 Y200 F3000 ;Present print\nM84 ;steppers off\nM300 P300 S4000\nM117 Finished.\n"}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_heated_bed": {"default_value": false}, "machine_height": {"default_value": 250}, "machine_max_acceleration_e": {"value": 5000}, "machine_max_acceleration_x": {"value": 500}, "machine_max_acceleration_y": {"value": 500}, "machine_max_acceleration_z": {"value": 100}, "machine_max_feedrate_e": {"value": 30}, "machine_max_feedrate_x": {"value": 500}, "machine_max_feedrate_y": {"value": 500}, "machine_max_feedrate_z": {"value": 5}, "machine_max_jerk_e": {"value": 15}, "machine_max_jerk_xy": {"value": 10}, "machine_max_jerk_z": {"value": 0.3}, "machine_name": {"default_value": "Anycubic Mega Zero"}, "machine_start_gcode": {"default_value": ";Sliced at: {day} {date} {time}\n;Basic settings: Layer height: {layer_height} Walls: {wall_thickness} Fill: {infill_sparse_density}\nG21 ;metric values\nG90 ;absolute positioning\nM82 ;set extruder to absolute mode\nM107 ;start with the fan off\nM117 Start heating ...\nM104 S{material_print_temperature_layer_0}\nM117 Homing X/Y ...\nG28 X0 Y0 ;move X/Y to min endstops\nM117 Homing Z ...\nG28 Z0 ;move Z to min endstops\nG1 Z15.0 F{speed_travel} ;move the platform down 15mm\nM117 Heating ...\nM109 S{material_print_temperature_layer_0}\nM117 Start cleaning ...\nG92 E0 ;zero the extruded length\nG1 F200 E10 ;extrude 10mm of feed stock\nG92 E0 ;zero the extruded length again\nM117 Intro line ...\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X0.1 Y20 Z{layer_height} F5000.0 ; Move to start position\nG1 X0.1 Y200.0 Z{layer_height} F1500.0 E15 ; Draw the first line\nG1 X0.4 Y200.0 Z{layer_height} F5000.0 ; Move to side a little\nG1 X0.4 Y20 Z0.3 F1500.0 E30 ; Draw the second line\nG92 E0 ; Reset Extruder\nG1 E-1 F500 ; Retract filiment by 1 mm\nG1 Z2.0 F3000 ; Move Z Axis up little to prevent scratching of Heat Bed\nG1 X5 Y20 Z0.3 F{speed_travel} ; Move over to prevent blob squish\nG1 F{speed_travel}\nG92 E0 ; Reset Extruder\nM117 Printing...\n"}, "machine_width": {"default_value": 220}, "material_final_print_temperature": {"value": "material_print_temperature"}, "material_initial_print_temperature": {"value": "material_print_temperature"}, "optimize_wall_printing_order": {"value": "True"}, "retraction_amount": {"value": 7}, "retraction_combing": {"value": "'noskin'"}, "retraction_combing_max_distance": {"value": 30}, "retraction_count_max": {"value": 100}, "retraction_extrusion_window": {"value": 10}, "retraction_hop": {"value": 0.2}, "retraction_hop_enabled": {"value": "True"}, "retraction_min_travel": {"value": 1.5}, "retraction_speed": {"value": 30}, "skirt_line_count": {"default_value": 3}, "speed_print": {"value": 50.0}, "speed_z_hop": {"value": "machine_max_feedrate_z"}, "support_xy_distance": {"value": "wall_line_width_0 * 2"}, "support_xy_distance_overhang": {"value": "wall_line_width_0"}, "support_z_distance": {"value": "layer_height if layer_height > 0.1 else layer_height*2"}, "travel_avoid_other_parts": {"value": true}, "travel_avoid_supports": {"value": true}, "travel_retract_before_outer_wall": {"value": true}}}