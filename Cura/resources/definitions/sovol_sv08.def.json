{"version": 2, "name": "Sovol SV08", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON><PERSON>", "manufacturer": "Sovol 3D", "file_formats": "text/x-gcode", "platform": "sovol_sv08_buildplate_model.stl", "exclude_materials": [], "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "sovol_sv08_extruder"}, "preferred_material": "generic_abs", "preferred_quality_type": "fast", "preferred_variant_name": "0.4mm Nozzle", "quality_definition": "sovol_sv08", "variants_name": "Nozzle Size"}, "overrides": {"acceleration_enabled": {"default_value": false}, "acceleration_layer_0": {"value": 1800}, "acceleration_print": {"default_value": 2200}, "acceleration_roofing": {"value": 1800}, "acceleration_travel_layer_0": {"value": 1800}, "acceleration_wall_0": {"value": 1800}, "adhesion_type": {"default_value": "skirt"}, "alternate_extra_perimeter": {"default_value": true}, "bridge_fan_speed": {"default_value": 100}, "bridge_fan_speed_2": {"resolve": "max(cool_fan_speed, 50)"}, "bridge_fan_speed_3": {"resolve": "max(cool_fan_speed, 20)"}, "bridge_settings_enabled": {"default_value": true}, "bridge_wall_coast": {"default_value": 10}, "cool_fan_full_at_height": {"value": "resolveOrValue('layer_height_0') + resolveOrValue('layer_height') * max(1, cool_fan_full_layer - 1)"}, "cool_fan_full_layer": {"value": 4}, "cool_fan_speed_min": {"value": "cool_fan_speed"}, "cool_min_layer_time": {"default_value": 15}, "cool_min_layer_time_fan_speed_max": {"default_value": 20}, "fill_outline_gaps": {"default_value": true}, "gantry_height": {"value": 30}, "infill_before_walls": {"default_value": false}, "infill_enable_travel_optimization": {"default_value": true}, "jerk_enabled": {"default_value": false}, "jerk_roofing": {"value": 10}, "jerk_wall_0": {"value": 10}, "layer_height_0": {"resolve": "max(0.2, min(extruderValues('layer_height')))"}, "line_width": {"value": "machine_nozzle_size * 1.125"}, "machine_acceleration": {"default_value": 1500}, "machine_depth": {"default_value": 350}, "machine_end_gcode": {"default_value": "END_PRINT\n"}, "machine_endstop_positive_direction_x": {"default_value": true}, "machine_endstop_positive_direction_y": {"default_value": true}, "machine_endstop_positive_direction_z": {"default_value": false}, "machine_feeder_wheel_diameter": {"default_value": 7.5}, "machine_gcode_flavor": {"default_value": "RepRap (Marlin/Sprinter)"}, "machine_head_with_fans_polygon": {"default_value": [[-35, 65], [-35, -50], [35, -50], [35, 65]]}, "machine_heated_bed": {"default_value": true}, "machine_height": {"default_value": 345}, "machine_max_acceleration_e": {"default_value": 5000}, "machine_max_acceleration_x": {"default_value": 40000}, "machine_max_acceleration_y": {"default_value": 40000}, "machine_max_acceleration_z": {"default_value": 500}, "machine_max_feedrate_e": {"default_value": 50}, "machine_max_feedrate_x": {"default_value": 700}, "machine_max_feedrate_y": {"default_value": 700}, "machine_max_feedrate_z": {"default_value": 20}, "machine_max_jerk_e": {"default_value": 5}, "machine_max_jerk_xy": {"default_value": 20}, "machine_max_jerk_z": {"default_value": 0.5}, "machine_name": {"default_value": "SV08"}, "machine_start_gcode": {"default_value": "G28 ; Move to zero\nG90 ; Absolute positioning\nG1 X0 F9000\nG1 Y20\nG1 Z0.600 F600\nG1 Y0 F9000\nSTART_PRINT EXTRUDER_TEMP={material_print_temperature_layer_0} BED_TEMP={material_bed_temperature_layer_0}\nG90 ; Absolute positioning (START_PRINT might have changed it)\nG1 X0 F9000\nG1 Y20\nG1 Z0.600 F600\nG1 Y0 F9000\nM400\nG91 ; Relative positioning\nM83 ; Relative extrusion\nM140 S{material_bed_temperature_layer_0} ; Set bed temp\nM104 S{material_print_temperature_layer_0} ; Set extruder temp\nM190 S{material_bed_temperature_layer_0} ; Wait for bed temp\nM109 S{material_print_temperature_layer_0} ; Wait for extruder temp\n{if machine_nozzle_size >= 0.4}\n; Standard Sovol blob and purge line.\nG1 E25 F300  ; Purge blob\nG4 P1000  ; Wait a bit to let it finish\nG1 E-0.200 Z5 F600  ; Retract\nG1 X88.000 F9000 ; Move right and then down for purge line\nG1 Z-5.000 F600\nG1 X87.000 E20.88 F1800  ; Purge line right\nG1 X87.000 E13.92 F1800\nG1 Y1 E0.16 F1800  ; Small movement back for next line\nG1 X-87.000 E13.92 F1800  ; Purge line left\nG1 X-87.000 E20.88 F1800\nG1 Y1 E0.24 F1800  ; Small movement back for next line\nG1 X87.000 E20.88 F1800  ; Purge line right\nG1 X87.000 E13.92 F1800\nG1 E-0.200 Z1 F600\n{else}\n; The default start G-code uses too high flow for smaller nozzles,\n; which causes Klipper errors. Scale everything back by\n; (0.25/0.4)^2, i.e., for 0.25mm nozzle. This should be good\n; enough for 0.2mm as well.\nG1 E8 F300  ; Purge blob\nG4 P1000  ; Wait a bit to let it finish\nG1 E-0.078 Z5 F600  ; Retract\nG1 X88.000 F9000  ; Move right and then down for purge line\nG1 Z-5.000 F600\nG1 X87.000 E8.16 F1800  ; Purge line right\nG1 X87.000 E5.44 F1800\nG1 Y1 E0.063 F1800 ; Small movement back for next line\nG1 X-87.000 E5.44 F1800  ; Purge line left\nG1 X-87.000 E8.16 F1800\nG1 Y1 E0.094 F1800  ; Small movement back for next line\nG1 X87.000 E8.16 F1800  ; Purge line right\nG1 X87.000 E5.44 F1800\nG1 E-0.078 Z1 F600\n{endif}\nM400  ; Wait for moves to finish\nG90 ; Absolute positioning\nM82 ; Absolute extrusion mode\n"}, "machine_steps_per_mm_x": {"default_value": 80}, "machine_steps_per_mm_y": {"default_value": 80}, "machine_steps_per_mm_z": {"default_value": 400}, "machine_width": {"default_value": 350}, "meshfix_maximum_resolution": {"default_value": 0.01}, "min_infill_area": {"default_value": 5.0}, "minimum_polygon_circumference": {"default_value": 0.2}, "optimize_wall_printing_order": {"default_value": true}, "retraction_amount": {"default_value": 0.5}, "retraction_combing": {"value": "'noskin'"}, "retraction_combing_max_distance": {"default_value": 10}, "retraction_hop": {"default_value": 0.4}, "retraction_hop_enabled": {"default_value": true}, "retraction_prime_speed": {"maximum_value_warning": 130, "value": "math.ceil(retraction_speed * 0.4)"}, "retraction_retract_speed": {"maximum_value_warning": 130}, "retraction_speed": {"default_value": 30, "maximum_value_warning": 130}, "roofing_layer_count": {"value": 1}, "skirt_brim_minimal_length": {"default_value": 550}, "speed_layer_0": {"value": "math.ceil(speed_print * 0.25)"}, "speed_roofing": {"value": "math.ceil(speed_print * 0.33)"}, "speed_slowdown_layers": {"default_value": 4}, "speed_topbottom": {"value": "math.ceil(speed_print * 0.33)"}, "speed_travel": {"maximum_value_warning": 501, "value": 300}, "speed_travel_layer_0": {"value": "math.ceil(speed_travel * 0.4)"}, "speed_wall": {"value": "math.ceil(speed_print * 0.33)"}, "speed_wall_0": {"value": "math.ceil(speed_print * 0.33)"}, "speed_wall_x": {"value": "math.ceil(speed_print * 0.66)"}, "travel_avoid_other_parts": {"default_value": false}, "wall_line_width": {"value": "machine_nozzle_size"}, "wall_overhang_angle": {"default_value": 75}, "wall_overhang_speed_factors": {"default_value": "[50]"}, "zig_zaggify_infill": {"value": true}}}