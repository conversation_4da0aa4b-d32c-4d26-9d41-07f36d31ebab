{"version": 2, "name": "<PERSON><PERSON><PERSON>", "inherits": "koonovo_base", "metadata": {"visible": true, "quality_definition": "koonovo_base"}, "overrides": {"gantry_height": {"value": 0}, "machine_depth": {"default_value": 310}, "machine_head_with_fans_polygon": {"default_value": [[-26, 34], [-26, -32], [32, -32], [32, 34]]}, "machine_height": {"default_value": 345}, "machine_name": {"default_value": "<PERSON><PERSON><PERSON>"}, "machine_width": {"default_value": 310}}}