{"version": 2, "name": "AnkerMake M5C", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "just-trey", "manufacturer": "AnkerMake", "file_formats": "text/x-gcode", "platform": "ankermake_m5c_platform.obj", "has_machine_quality": true, "has_textured_buildplate": true, "machine_extruder_trains": {"0": "ankermake_m5c_extruder_0"}, "platform_texture": "ankermake_m5c.png", "preferred_material": "generic_pla", "preferred_quality_type": "normal"}, "overrides": {"acceleration_enabled": {"value": true}, "acceleration_infill": {"value": 5000}, "acceleration_layer_0": {"value": 2500}, "acceleration_prime_tower": {"value": 5000}, "acceleration_print": {"value": 5000}, "acceleration_print_layer_0": {"value": 2500}, "acceleration_roofing": {"value": 2500}, "acceleration_skirt_brim": {"value": 2500}, "acceleration_support": {"value": 5000}, "acceleration_support_bottom": {"value": 5000}, "acceleration_support_infill": {"value": 5000}, "acceleration_support_interface": {"value": 5000}, "acceleration_support_roof": {"value": 5000}, "acceleration_topbottom": {"value": 2500}, "acceleration_travel_layer_0": {"value": 2500}, "acceleration_wall": {"value": 5000}, "acceleration_wall_x": {"value": 5000}, "adhesion_type": {"default_value": "skirt"}, "alternate_extra_perimeter": {"value": true}, "bottom_layers": {"value": 3}, "bottom_skin_expand_distance": {"value": 0.84}, "bottom_skin_preshrink": {"value": 0.84}, "bottom_thickness": {"value": 0.8}, "bridge_fan_speed_2": {"value": 100}, "bridge_fan_speed_3": {"value": 100}, "bridge_settings_enabled": {"value": true}, "bridge_skin_density_2": {"value": 80}, "bridge_skin_material_flow": {"value": 100}, "bridge_skin_material_flow_2": {"value": 80}, "bridge_skin_speed": {"value": 20}, "bridge_skin_speed_2": {"value": 50}, "bridge_skin_speed_3": {"value": 50}, "bridge_wall_material_flow": {"value": 100}, "bridge_wall_speed": {"value": 20}, "connect_infill_polygons": {"value": false}, "cool_fan_full_at_height": {"value": 0.14}, "cool_min_layer_time": {"value": 6}, "cool_min_speed": {"value": 30}, "cross_infill_pocket_size": {"value": 8}, "expand_skins_expand_distance": {"value": 0.84}, "fill_outline_gaps": {"value": false}, "gantry_height": {"value": 25}, "gradual_infill_step_height": {"value": 2}, "infill_angles": {"value": [90]}, "infill_extruder_nr": {"value": -1}, "infill_material_flow": {"value": 90}, "infill_pattern": {"value": "'lines' if infill_sparse_density >= 25 else 'grid'"}, "infill_sparse_density": {"value": 10}, "infill_sparse_thickness": {"value": 0.25}, "infill_wipe_dist": {"value": 0.1}, "initial_bottom_layers": {"value": 3}, "jerk_enabled": {"value": true}, "jerk_infill": {"value": 15}, "jerk_layer_0": {"value": 15}, "jerk_prime_tower": {"value": 15}, "jerk_print": {"value": 15}, "jerk_print_layer_0": {"value": 15}, "jerk_roofing": {"value": 15}, "jerk_skirt_brim": {"value": 15}, "jerk_support": {"value": 15}, "jerk_support_bottom": {"value": 15}, "jerk_support_infill": {"value": 15}, "jerk_support_interface": {"value": 15}, "jerk_support_roof": {"value": 15}, "jerk_topbottom": {"value": 15}, "jerk_travel": {"value": 15}, "jerk_travel_layer_0": {"value": 15}, "jerk_wall": {"value": 15}, "jerk_wall_0": {"value": 15}, "jerk_wall_x": {"value": 15}, "machine_buildplate_type": {"value": "glass"}, "machine_depth": {"value": 220}, "machine_heated_bed": {"value": true}, "machine_height": {"value": 250}, "machine_max_jerk_e": {"value": 5}, "machine_max_jerk_xy": {"value": 30}, "machine_max_jerk_z": {"value": 0.3}, "machine_name": {"default_value": "AnkerMake M5"}, "machine_shape": {"value": "rectangular"}, "machine_show_variants": {"value": false}, "machine_start_gcode": {"default_value": "M104 S{material_print_temperature_layer_0} ; set final nozzle temp\nM190 S{material_bed_temperature_layer_0} ; set and wait for nozzle temp to stabilize\nM109 S{material_print_temperature_layer_0} ; wait for nozzle temp to stabilize\nG28 ;Home\nG1 E10 F3600; push out retracted filament(fix for over retraction after prime)"}, "machine_width": {"value": 220}, "material_diameter": {"default_value": 1.75}, "material_flow_layer_0": {"value": 120}, "material_no_load_move_factor": {"value": 0.94}, "minimum_interface_area": {"value": 10}, "minimum_support_area": {"value": "2 if support_structure == 'normal' else 0"}, "retract_at_layer_change": {"value": true}, "retraction_amount": {"value": 0.8}, "retraction_combing": {"value": "noskin"}, "retraction_combing_max_distance": {"value": 3}, "retraction_extrusion_window": {"value": 0.8}, "retraction_min_travel": {"value": 0.8}, "retraction_prime_speed": {"value": 60}, "retraction_retract_speed": {"value": 60}, "retraction_speed": {"value": 60}, "roofing_angles": {"value": []}, "roofing_monotonic": {"value": false}, "roofing_pattern": {"value": "zigzag"}, "skin_material_flow": {"value": 97}, "skin_monotonic": {"default_value": true}, "skirt_brim_speed": {"maximum_value_warning": "550", "value": 50}, "skirt_line_count": {"value": 3}, "small_feature_max_length": {"value": 9.42}, "small_hole_max_size": {"value": 3}, "speed_infill": {"maximum_value_warning": "550", "value": 270}, "speed_layer_0": {"maximum_value_warning": "550", "value": 50}, "speed_prime_tower": {"maximum_value_warning": "550", "value": 500}, "speed_print": {"maximum_value_warning": "550", "value": 500}, "speed_print_layer_0": {"maximum_value_warning": "550", "value": 50}, "speed_roofing": {"maximum_value_warning": "550", "value": 150}, "speed_support": {"maximum_value_warning": "550", "value": 250}, "speed_support_bottom": {"maximum_value_warning": "550", "value": 166.667}, "speed_support_infill": {"maximum_value_warning": "550", "value": 250}, "speed_support_interface": {"maximum_value_warning": "550", "value": 166.667}, "speed_support_roof": {"maximum_value_warning": "550", "value": 166.667}, "speed_topbottom": {"maximum_value_warning": "550", "value": 150}, "speed_travel": {"maximum_value_warning": "550", "value": 500}, "speed_travel_layer_0": {"maximum_value_warning": "550", "value": 150}, "speed_wall": {"maximum_value_warning": "550", "value": 250}, "speed_wall_0": {"maximum_value_warning": "550", "value": 150}, "speed_wall_x": {"maximum_value_warning": "550", "value": 250}, "speed_wall_x_roofing": {"maximum_value_warning": "550"}, "support_bottom_distance": {"value": 0.2}, "support_brim_enable": {"value": false}, "support_brim_line_count": {"value": 20}, "support_brim_width": {"value": 8}, "support_infill_angles": {"value": []}, "support_infill_rate": {"value": 30}, "support_initial_layer_line_distance": {"value": 1.333}, "support_line_distance": {"value": 1.333}, "support_offset": {"value": 2}, "support_top_distance": {"value": 0.2}, "support_xy_distance": {"value": 0.8}, "support_xy_overrides_z": {"value": "xy_overrides_z"}, "top_layers": {"value": 4}, "top_skin_expand_distance": {"value": 0.84}, "top_skin_preshrink": {"value": 0.84}, "travel_avoid_distance": {"value": 0.63}, "wall_0_extruder_nr": {"value": -1}, "wall_extruder_nr": {"value": -1}, "wall_line_width_0": {"value": 0.44}, "wall_overhang_angle": {"value": 45}, "wall_overhang_speed_factors": {"value": [40]}, "wall_thickness": {"value": 0.84}, "wall_x_extruder_nr": {"value": -1}, "zig_zaggify_infill": {"value": true}}}