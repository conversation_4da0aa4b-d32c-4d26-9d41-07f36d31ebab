{"version": 2, "name": "HMS434", "inherits": "fdmprinter", "metadata": {"visible": true, "author": "<PERSON><PERSON><PERSON>", "manufacturer": "Hybrid AM Systems", "file_formats": "text/x-gcode", "platform": "hms_platform.obj", "exclude_materials": ["dsm_arnitel2045", "dsm_novamid1070", "generic_abs", "generic_cpe", "generic_hips", "generic_nylon", "generic_pc", "generic_petg", "generic_pva", "generic_tpu", "imade3d_petg", "imade3d_pla", "innofill_innoflex60", "verbatim_bvoh"], "first_start_actions": ["MachineSettingsAction"], "has_machine_quality": true, "has_materials": true, "has_variants": true, "machine_extruder_trains": {"0": "hms434_tool_1", "1": "hms434_tool_2", "2": "hms434_tool_3", "3": "hms434_tool_4", "4": "hms434_tool_5", "5": "hms434_tool_6", "6": "hms434_tool_7", "7": "hms434_tool_8"}, "platform_offset": [26, -13.2, 162.5], "platform_texture": "hms434.png", "preferred_quality_type": "high", "preferred_variant_name": "0.4mm TP extruder", "supported_actions": ["MachineSettingsAction"], "variants_name": "Tool"}, "overrides": {"acceleration_print": {"value": 1000}, "acceleration_travel": {"value": 1000}, "adhesion_type": {"value": "'brim'"}, "alternate_extra_perimeter": {"value": false}, "bottom_layers": {"value": "(top_layers)"}, "bridge_fan_speed": {"value": 0}, "bridge_settings_enabled": {"value": true}, "bridge_skin_density_2": {"value": 100}, "bridge_skin_density_3": {"value": 100}, "bridge_skin_material_flow": {"value": 130}, "bridge_skin_material_flow_2": {"value": 110}, "bridge_skin_material_flow_3": {"value": 100}, "bridge_skin_speed": {"value": 15}, "bridge_skin_speed_2": {"value": 20}, "bridge_skin_speed_3": {"value": 30}, "bridge_skin_support_threshold": {"value": 90}, "bridge_wall_material_flow": {"value": 130}, "bridge_wall_min_length": {"value": 3}, "bridge_wall_speed": {"value": 15}, "coasting_enable": {"value": false}, "coasting_min_volume": {"value": 0.17}, "coasting_speed": {"value": 90}, "coasting_volume": {"value": 0.1}, "cool_fan_enabled": {"value": true}, "cool_fan_speed": {"value": 0}, "cool_lift_head": {"value": false}, "cool_min_layer_time": {"value": 30}, "cool_min_layer_time_fan_speed_max": {"value": "cool_min_layer_time"}, "cool_min_speed": {"value": "5"}, "default_material_print_temperature": {"maximum_value": "401"}, "fill_outline_gaps": {"value": true}, "gantry_height": {"value": "35"}, "infill_before_walls": {"value": true}, "infill_pattern": {"value": "'lines'"}, "infill_sparse_density": {"value": 100}, "initial_layer_line_width_factor": {"value": 110}, "inset_direction": {"value": "'outside_in'"}, "ironing_flow": {"value": "0"}, "ironing_inset": {"value": "ironing_line_spacing + (ironing_line_spacing - skin_line_width * (1.0 + ironing_flow / 100) / 2 if ironing_pattern == 'concentric' else skin_line_width * (1.0 - ironing_flow / 100) / 2)"}, "ironing_line_spacing": {"value": "line_width / 4 * 3"}, "jerk_print": {"value": 10}, "jerk_travel": {"value": 10}, "layer_height": {"maximum_value": "(0.8 * min(extruderValues('machine_nozzle_size')))"}, "layer_height_0": {"maximum_value": "(0.8 * min(extruderValues('machine_nozzle_size')))"}, "machine_acceleration": {"default_value": 180}, "machine_center_is_zero": {"default_value": false}, "machine_depth": {"default_value": 325}, "machine_end_gcode": {"default_value": ""}, "machine_extruder_count": {"default_value": 2}, "machine_gcode_flavor": {"default_value": "RepRap (RepRap)"}, "machine_heated_bed": {"default_value": true}, "machine_heated_build_volume": {"default_value": true}, "machine_height": {"default_value": 400}, "machine_max_feedrate_z": {"default_value": 10}, "machine_min_cool_heat_time_window": {"default_value": 5}, "machine_nozzle_cool_down_speed": {"default_value": 20}, "machine_nozzle_heat_up_speed": {"default_value": 12}, "machine_start_gcode": {"default_value": "\n;Neither Hybrid AM Systems nor any of Hybrid AM Systems representatives has any liabilities or gives any warranties on this .gcode file, or on any or all objects made with this .gcode file.\n\nM114\n\nM140 S{material_bed_temperature_layer_0}\nM118 // action:chamber_fan_on\nM141 S{build_volume_temperature}\n\nM117 Homing Y ......\nG28 Y\nM117 Homing X ......\nG28 X\nM117 Homing Z ......\nG28 Z F100\n\nG1 Z10 F900\nG1 X-25 Y20 F12000\n\nM190 S{material_bed_temperature_layer_0}\n\nM117 HMS434 Printing ..."}, "machine_width": {"default_value": 450}, "material_bed_temp_wait": {"default_value": false}, "material_bed_temperature_layer_0": {"value": "material_bed_temperature"}, "material_break_preparation_temperature": {"maximum_value": "401"}, "material_diameter": {"default_value": 1.75}, "material_final_print_temperature": {"maximum_value": "401", "value": "material_print_temperature"}, "material_flow_layer_0": {"value": "material_flow"}, "material_initial_print_temperature": {"maximum_value": "401", "maximum_value_warning": "material_print_temperature + 15", "value": "material_print_temperature"}, "material_print_temp_wait": {"default_value": false}, "material_print_temperature": {"maximum_value": "401"}, "material_print_temperature_layer_0": {"maximum_value": "401"}, "meshfix_maximum_deviation": {"value": 0.005}, "meshfix_maximum_resolution": {"value": 0.01}, "meshfix_maximum_travel_resolution": {"value": 0.1}, "minimum_polygon_circumference": {"value": 0.05}, "optimize_wall_printing_order": {"default_value": true}, "prime_blob_enable": {"default_value": false}, "prime_tower_size": {"value": 20.6}, "retract_at_layer_change": {"value": false}, "retraction_combing": {"value": "'off'"}, "retraction_enable": {"value": true}, "retraction_extra_prime_amount": {"minimum_value_warning": "-2.0"}, "retraction_hop": {"value": 1}, "retraction_hop_enabled": {"value": false}, "retraction_min_travel": {"value": "(round(line_width * 10))"}, "roofing_layer_count": {"value": "0"}, "skin_outline_count": {"value": "0"}, "skirt_brim_minimal_length": {"value": 50}, "skirt_gap": {"value": 1}, "speed_ironing": {"value": "150"}, "speed_layer_0": {"value": "(speed_print / 5 * 4) if speed_print > 45 else speed_print"}, "speed_print": {"value": "50"}, "speed_slowdown_layers": {"value": 1}, "speed_support_interface": {"value": "speed_topbottom"}, "speed_topbottom": {"value": "speed_layer_0"}, "speed_travel": {"value": "250"}, "speed_travel_layer_0": {"value": "speed_travel"}, "speed_wall": {"value": "(speed_print/ 5 * 3) if speed_print > 45 else speed_print"}, "speed_wall_x": {"value": "speed_wall"}, "speed_z_hop": {"value": 10}, "support_bottom_height": {"value": "layer_height"}, "support_infill_rate": {"value": 30}, "support_interface_enable": {"value": true}, "support_interface_height": {"value": "layer_height * 3"}, "support_interface_pattern": {"value": "'lines'"}, "support_join_distance": {"value": 10}, "support_pattern": {"value": "'grid'"}, "support_roof_pattern": {"value": "'concentric'"}, "support_xy_distance": {"value": 0.4}, "support_z_distance": {"value": 0}, "switch_extruder_prime_speed": {"value": "(retraction_prime_speed)"}, "switch_extruder_retraction_speeds": {"value": "(retraction_speed)"}, "top_bottom_thickness": {"value": "(layer_height_0 + (layer_height * (top_layers - 1)))"}, "top_layers": {"value": "4 if infill_sparse_density < 95 else 1"}, "wall_0_inset": {"value": "0"}, "wall_overhang_angle": {"value": 60}, "wall_thickness": {"value": "(line_width * 3) if infill_sparse_density < 95 else line_width"}, "z_seam_corner": {"value": "'z_seam_corner_inner'"}, "z_seam_type": {"value": "'shortest'"}, "z_seam_x": {"value": "300"}, "z_seam_y": {"value": "325"}}}