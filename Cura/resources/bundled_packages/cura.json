{"3DConnexion": {"package_info": {"package_id": "3DConnexion", "package_type": "plugin", "display_name": "3DConnexion mouses", "description": "Allows working with 3D mouses inside Cura.\nOnly available on Windows and MacOS.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://3dconnexion.com", "author": {"author_id": "UltimakerPackages", "display_name": "3DConnexion", "email": "<EMAIL>", "website": "https://3dconnexion.com"}}}, "3MFReader": {"package_info": {"package_id": "3<PERSON><PERSON><PERSON><PERSON>", "package_type": "plugin", "display_name": "3MF Reader", "description": "Provides support for reading 3MF files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "3MFWriter": {"package_info": {"package_id": "3MFWriter", "package_type": "plugin", "display_name": "3MF Writer", "description": "Provides support for writing 3MF files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "AMFReader": {"package_info": {"package_id": "AMFReader", "package_type": "plugin", "display_name": "AMF Reader", "description": "Provides support for reading AMF files.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "fieldOfView", "display_name": "fieldOfView", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "CuraDrive": {"package_info": {"package_id": "CuraDrive", "package_type": "plugin", "display_name": "<PERSON><PERSON>ups", "description": "Backup and restore your configuration.", "package_version": "1.2.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "CuraEngineBackend": {"package_info": {"package_id": "CuraEngineBackend", "package_type": "plugin", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Provides the link to the CuraEngine slicing backend.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "CuraProfileReader": {"package_info": {"package_id": "CuraProfileReader", "package_type": "plugin", "display_name": "Cura Profile Reader", "description": "Provides support for importing Cura profiles.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "CuraProfileWriter": {"package_info": {"package_id": "CuraProfileWriter", "package_type": "plugin", "display_name": "Cura Profile Writer", "description": "Provides support for exporting Cura profiles.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "DigitalLibrary": {"package_info": {"package_id": "DigitalLibrary", "package_type": "plugin", "display_name": "Ultimaker Digital Library", "description": "Connects to the Digital Library, allowing <PERSON><PERSON> to open files from and save files to the Digital Library.", "package_version": "1.1.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "FirmwareUpdateChecker": {"package_info": {"package_id": "FirmwareUpdateChecker", "package_type": "plugin", "display_name": "Firmware Update Checker", "description": "Checks for firmware updates.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "FirmwareUpdater": {"package_info": {"package_id": "FirmwareUpdater", "package_type": "plugin", "display_name": "Firmware Updater", "description": "Provides a machine actions for updating firmware.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "GCodeGzReader": {"package_info": {"package_id": "GCodeGzReader", "package_type": "plugin", "display_name": "Compressed G-code Reader", "description": "Reads g-code from a compressed archive.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "GCodeGzWriter": {"package_info": {"package_id": "GCodeGzWriter", "package_type": "plugin", "display_name": "Compressed G-code Writer", "description": "Writes g-code to a compressed archive.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "GCodeProfileReader": {"package_info": {"package_id": "GCodeProfileReader", "package_type": "plugin", "display_name": "G-Code Profile Reader", "description": "Provides support for importing profiles from g-code files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "GCodeReader": {"package_info": {"package_id": "GCodeReader", "package_type": "plugin", "display_name": "G-Code Reader", "description": "Allows loading and displaying G-code files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "<PERSON><PERSON><PERSON><PERSON>", "display_name": "<PERSON>", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "GCodeWriter": {"package_info": {"package_id": "GCodeWriter", "package_type": "plugin", "display_name": "G-Code Writer", "description": "Writes g-code to a file.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "ImageReader": {"package_info": {"package_id": "ImageReader", "package_type": "plugin", "display_name": "Image Reader", "description": "Enables ability to generate printable geometry from 2D image files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "LegacyProfileReader": {"package_info": {"package_id": "LegacyProfileReader", "package_type": "plugin", "display_name": "Legacy Cura Profile Reader", "description": "Provides support for importing profiles from legacy Cura versions.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "MachineSettingsAction": {"package_info": {"package_id": "MachineSettingsAction", "package_type": "plugin", "display_name": "Machine Settings Action", "description": "Provides a way to change machine settings (such as build volume, nozzle size, etc.).", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "fieldOfView", "display_name": "fieldOfView", "email": null, "website": "https://ultimaker.com"}}}, "MakerbotWriter": {"package_info": {"package_id": "MakerbotWriter", "package_type": "plugin", "display_name": "Makerbot Printfile Writer", "description": "Provides support for writing MakerBot Format Packages.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "ModelChecker": {"package_info": {"package_id": "<PERSON><PERSON><PERSON><PERSON>", "package_type": "plugin", "display_name": "Model Checker", "description": "Checks models and print configuration for possible printing issues and give suggestions.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "MonitorStage": {"package_info": {"package_id": "MonitorStage", "package_type": "plugin", "display_name": "Monitor Stage", "description": "Provides a monitor stage in Cura.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "PerObjectSettingsTool": {"package_info": {"package_id": "PerObjectSettingsTool", "package_type": "plugin", "display_name": "Per-Object Settings Tool", "description": "Provides the per-model settings.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "PostProcessingPlugin": {"package_info": {"package_id": "PostProcessingPlugin", "package_type": "plugin", "display_name": "Post Processing", "description": "Extension that allows for user created scripts for post processing.", "package_version": "2.2.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "PrepareStage": {"package_info": {"package_id": "PrepareStage", "package_type": "plugin", "display_name": "Prepare Stage", "description": "Provides a prepare stage in Cura.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "PreviewStage": {"package_info": {"package_id": "PreviewStage", "package_type": "plugin", "display_name": "Preview Stage", "description": "Provides a preview stage in Cura.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "RemovableDriveOutputDevice": {"package_info": {"package_id": "RemovableDriveOutputDevice", "package_type": "plugin", "display_name": "Removable Drive Output Device", "description": "Provides removable drive hotplugging and writing support.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "SentryLogger": {"package_info": {"package_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "plugin", "display_name": "<PERSON><PERSON>", "description": "Logs certain events so that they can be used by the crash reporter", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "SimulationView": {"package_info": {"package_id": "SimulationView", "package_type": "plugin", "display_name": "Simulation View", "description": "Provides the Simulation view.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "SliceInfoPlugin": {"package_info": {"package_id": "SliceInfoPlugin", "package_type": "plugin", "display_name": "Slice Info", "description": "Submits anonymous slice info. Can be disabled through preferences.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "SolidView": {"package_info": {"package_id": "SolidView", "package_type": "plugin", "display_name": "Solid View", "description": "Provides a normal solid mesh view.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "SupportEraser": {"package_info": {"package_id": "SupportEraser", "package_type": "plugin", "display_name": "Support Eraser Tool", "description": "Creates an eraser mesh to block the printing of support in certain places.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "TrimeshReader": {"package_info": {"package_id": "<PERSON><PERSON>h<PERSON><PERSON><PERSON>", "package_type": "plugin", "display_name": "Trimesh Reader", "description": "Provides support for reading model files.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "Marketplace": {"package_info": {"package_id": "Marketplace", "package_type": "plugin", "display_name": "Marketplace", "description": "Find, manage and install new Cura packages.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "UFPReader": {"package_info": {"package_id": "UFPReader", "package_type": "plugin", "display_name": "UFP Reader", "description": "Provides support for reading Ultimaker Format Packages.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "UFPWriter": {"package_info": {"package_id": "UFPWriter", "package_type": "plugin", "display_name": "UFP Writer", "description": "Provides support for writing Ultimaker Format Packages.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "UltimakerMachineActions": {"package_info": {"package_id": "UltimakerMachineActions", "package_type": "plugin", "display_name": "Ultimaker Machine Actions", "description": "Provides machine actions for Ultimaker machines (such as bed leveling wizard, selecting upgrades, etc.).", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "UM3NetworkPrinting": {"package_info": {"package_id": "UM3NetworkPrinting", "package_type": "plugin", "display_name": "UM3 Network Printing", "description": "Manages network connections to Ultimaker 3 printers.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "USBPrinting": {"package_info": {"package_id": "USBPrinting", "package_type": "plugin", "display_name": "USB Printing", "description": "Accepts G-Code and sends them to a printer. Plugin can also update firmware.", "package_version": "1.0.2", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade21to22": {"package_info": {"package_id": "VersionUpgrade21to22", "package_type": "plugin", "display_name": "Version Upgrade 2.1 to 2.2", "description": "Upgrades configurations from Cura 2.1 to Cura 2.2.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade22to24": {"package_info": {"package_id": "VersionUpgrade22to24", "package_type": "plugin", "display_name": "Version Upgrade 2.2 to 2.4", "description": "Upgrades configurations from Cura 2.2 to Cura 2.4.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade25to26": {"package_info": {"package_id": "VersionUpgrade25to26", "package_type": "plugin", "display_name": "Version Upgrade 2.5 to 2.6", "description": "Upgrades configurations from Cura 2.5 to Cura 2.6.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade26to27": {"package_info": {"package_id": "VersionUpgrade26to27", "package_type": "plugin", "display_name": "Version Upgrade 2.6 to 2.7", "description": "Upgrades configurations from Cura 2.6 to Cura 2.7.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade27to30": {"package_info": {"package_id": "VersionUpgrade27to30", "package_type": "plugin", "display_name": "Version Upgrade 2.7 to 3.0", "description": "Upgrades configurations from Cura 2.7 to Cura 3.0.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade30to31": {"package_info": {"package_id": "VersionUpgrade30to31", "package_type": "plugin", "display_name": "Version Upgrade 3.0 to 3.1", "description": "Upgrades configurations from Cura 3.0 to Cura 3.1.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade32to33": {"package_info": {"package_id": "VersionUpgrade32to33", "package_type": "plugin", "display_name": "Version Upgrade 3.2 to 3.3", "description": "Upgrades configurations from Cura 3.2 to Cura 3.3.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade33to34": {"package_info": {"package_id": "VersionUpgrade33to34", "package_type": "plugin", "display_name": "Version Upgrade 3.3 to 3.4", "description": "Upgrades configurations from Cura 3.3 to Cura 3.4.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade34to35": {"package_info": {"package_id": "VersionUpgrade34to35", "package_type": "plugin", "display_name": "Version Upgrade 3.4 to 3.5", "description": "Upgrades configurations from Cura 3.4 to Cura 3.5.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade35to40": {"package_info": {"package_id": "VersionUpgrade35to40", "package_type": "plugin", "display_name": "Version Upgrade 3.5 to 4.0", "description": "Upgrades configurations from Cura 3.5 to Cura 4.0.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade40to41": {"package_info": {"package_id": "VersionUpgrade40to41", "package_type": "plugin", "display_name": "Version Upgrade 4.0 to 4.1", "description": "Upgrades configurations from Cura 4.0 to Cura 4.1.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade41to42": {"package_info": {"package_id": "VersionUpgrade41to42", "package_type": "plugin", "display_name": "Version Upgrade 4.1 to 4.2", "description": "Upgrades configurations from Cura 4.1 to Cura 4.2.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade42to43": {"package_info": {"package_id": "VersionUpgrade42to43", "package_type": "plugin", "display_name": "Version Upgrade 4.2 to 4.3", "description": "Upgrades configurations from Cura 4.2 to Cura 4.3.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade43to44": {"package_info": {"package_id": "VersionUpgrade43to44", "package_type": "plugin", "display_name": "Version Upgrade 4.3 to 4.4", "description": "Upgrades configurations from Cura 4.3 to Cura 4.4.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade44to45": {"package_info": {"package_id": "VersionUpgrade44to45", "package_type": "plugin", "display_name": "Version Upgrade 4.4 to 4.5", "description": "Upgrades configurations from Cura 4.4 to Cura 4.5.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade45to46": {"package_info": {"package_id": "VersionUpgrade45to46", "package_type": "plugin", "display_name": "Version Upgrade 4.5 to 4.6", "description": "Upgrades configurations from Cura 4.5 to Cura 4.6.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade460to462": {"package_info": {"package_id": "VersionUpgrade460to462", "package_type": "plugin", "display_name": "Version Upgrade 4.6.0 to 4.6.2", "description": "Upgrades configurations from Cura 4.6.0 to Cura 4.6.2.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade462to47": {"package_info": {"package_id": "VersionUpgrade462to47", "package_type": "plugin", "display_name": "Version Upgrade 4.6.2 to 4.7", "description": "Upgrades configurations from Cura 4.6.2 to Cura 4.7.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade47to48": {"package_info": {"package_id": "VersionUpgrade47to48", "package_type": "plugin", "display_name": "Version Upgrade 4.7.0 to 4.8.0", "description": "Upgrades configurations from Cura 4.7.0 to Cura 4.8.0", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade48to49": {"package_info": {"package_id": "VersionUpgrade48to49", "package_type": "plugin", "display_name": "Version Upgrade 4.8.0 to 4.9.0", "description": "Upgrades configurations from Cura 4.8.0 to Cura 4.9.0", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade49to410": {"package_info": {"package_id": "VersionUpgrade49to410", "package_type": "plugin", "display_name": "Version Upgrade 4.9 to 4.10", "description": "Upgrades configurations from Cura 4.9 to Cura 4.10", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade411to412": {"package_info": {"package_id": "VersionUpgrade411to412", "package_type": "plugin", "display_name": "Version Upgrade 4.11 to 4.12", "description": "Upgrades configurations from Cura 4.11 to Cura 4.12", "package_version": "1.0.0", "sdk_version": "8.6.0", "sdk_version_semver": "7.7.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade413to50": {"package_info": {"package_id": "VersionUpgrade413to50", "package_type": "plugin", "display_name": "Version Upgrade 4.13 to 5.0", "description": "Upgrades configurations from Cura 4.13 to Cura 5.0", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "VersionUpgrade52to53": {"package_info": {"package_id": "VersionUpgrade52to53", "package_type": "plugin", "display_name": "Version Upgrade 5.2 to 5.3", "description": "Upgrades configurations from Cura 5.2 to Cura 5.3", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "X3DReader": {"package_info": {"package_id": "X3DReader", "package_type": "plugin", "display_name": "X3D Reader", "description": "Provides support for reading X3D files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "display_name": "<PERSON><PERSON>", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "XmlMaterialProfile": {"package_info": {"package_id": "XMLMaterialProfile", "package_type": "plugin", "display_name": "XML Material Profiles", "description": "Provides capabilities to read and write XML-based material profiles.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "XRayView": {"package_info": {"package_id": "XRayView", "package_type": "plugin", "display_name": "X-Ray View", "description": "Provides the X-Ray view.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "GenericABS": {"package_info": {"package_id": "GenericABS", "package_type": "material", "display_name": "Generic ABS", "description": "The generic ABS profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericBAM": {"package_info": {"package_id": "GenericBAM", "package_type": "material", "display_name": "Generic BAM", "description": "The generic BAM profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericCFFCPE": {"package_info": {"package_id": "GenericCFFCPE", "package_type": "material", "display_name": "Generic CFF CPE", "description": "The generic CFF CPE profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericCFFPA": {"package_info": {"package_id": "GenericCFFPA", "package_type": "material", "display_name": "Generic CFF PA", "description": "The generic CFF PA profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericCPE": {"package_info": {"package_id": "GenericCPE", "package_type": "material", "display_name": "Generic CPE", "description": "The generic CPE profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericCPEPlus": {"package_info": {"package_id": "GenericCPEPlus", "package_type": "material", "display_name": "Generic CPE+", "description": "The generic CPE+ profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericGFFCPE": {"package_info": {"package_id": "GenericGFFCPE", "package_type": "material", "display_name": "Generic GFF CPE", "description": "The generic GFF CPE profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericGFFPA": {"package_info": {"package_id": "GenericGFFPA", "package_type": "material", "display_name": "Generic GFF PA", "description": "The generic GFF PA profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericHIPS": {"package_info": {"package_id": "GenericHIPS", "package_type": "material", "display_name": "Generic HIPS", "description": "The generic HIPS profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericNylon": {"package_info": {"package_id": "GenericNylon", "package_type": "material", "display_name": "Generic Nylon", "description": "The generic Nylon profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericPC": {"package_info": {"package_id": "GenericPC", "package_type": "material", "display_name": "Generic PC", "description": "The generic PC profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericPETCF": {"package_info": {"package_id": "GenericPETCF", "package_type": "material", "display_name": "Generic PETCF", "description": "The generic PET-CF profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericPETG": {"package_info": {"package_id": "GenericPETG", "package_type": "material", "display_name": "Generic PETG", "description": "The generic PETG profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericPLA": {"package_info": {"package_id": "GenericPLA", "package_type": "material", "display_name": "Generic PLA", "description": "The generic PLA profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericPP": {"package_info": {"package_id": "GenericPP", "package_type": "material", "display_name": "Generic PP", "description": "The generic PP profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericPVA": {"package_info": {"package_id": "GenericPVA", "package_type": "material", "display_name": "Generic PVA", "description": "The generic PVA profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericToughPLA": {"package_info": {"package_id": "GenericToughPLA", "package_type": "material", "display_name": "<PERSON><PERSON>", "description": "The generic Tough PLA profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "GenericTPU": {"package_info": {"package_id": "GenericTPU", "package_type": "material", "display_name": "Generic TPU", "description": "The generic TPU profile which other profiles can be based upon.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://github.com/Ultimaker/fdm_materials", "author": {"author_id": "Generic", "display_name": "Generic", "email": "<EMAIL>", "website": "https://github.com/Ultimaker/fdm_materials", "description": "Professional 3D printing made accessible."}}}, "BASFUltrafuse316L": {"package_info": {"package_id": "BASFUltrafuse316L", "package_type": "material", "display_name": "BASF Ultrafuse 316L", "description": "An innovative filament to produce 316L grade stainless steel parts.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://forward-am.com/material-portfolio/ultrafuse-filaments-for-fused-filaments-fabrication-fff/metal-filaments/ultrafuse-316l/", "author": {"author_id": "BASF", "display_name": "BASF", "email": null, "website": "https://forward-am.com/"}}}, "DagomaChromatikPLA": {"package_info": {"package_id": "DagomaChromatikPLA", "package_type": "material", "display_name": "Dagoma Chromatik PLA", "description": "Filament testé et approuvé pour les imprimantes 3D Dagoma. Chromatik est l'idéal pour débuter et suivre les tutoriels premiers pas. Il vous offre qualité et résistance pour chacune de vos impressions.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://dagoma.fr/boutique/filaments.html", "author": {"author_id": "Dagoma", "display_name": "Dagoma", "email": null, "website": "https://dagoma.fr"}}}, "FABtotumABS": {"package_info": {"package_id": "FABtotumABS", "package_type": "material", "display_name": "FABtotum ABS", "description": "This material is easy to be extruded but it is not the simplest to use. It is one of the most used in 3D printing to get very well finished objects. It is not sustainable and its smoke can be dangerous if inhaled. The reason to prefer this filament to PLA is mainly because of its precision and mechanical specs. ABS (for plastic) stands for Acrylonitrile Butadiene Styrene and it is a thermoplastic which is widely used in everyday objects. It can be printed with any FFF 3D printer which can get to high temperatures as it must be extruded in a range between 220° and 245°, so it’s compatible with all versions of the FABtotum Personal fabricator.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://store.fabtotum.com/eu/products/filaments.html?filament_type=40", "author": {"author_id": "FABtotum", "display_name": "FABtotum S.R.L.", "email": "<EMAIL>", "website": "https://www.fabtotum.com"}}}, "FABtotumNylon": {"package_info": {"package_id": "FABtotumNylon", "package_type": "material", "display_name": "FABtotum Nylon", "description": "When 3D printing started this material was not listed among the extrudable filaments. It is flexible as well as resistant to tractions. It is well known for its uses in textile but also in industries which require a strong and flexible material. There are different kinds of Nylon: 3D printing mostly uses Nylon 6 and Nylon 6.6, which are the most common. It requires higher temperatures to be printed, so a 3D printer must be able to reach them (around 240°C): the FABtotum, of course, can.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://store.fabtotum.com/eu/products/filaments.html?filament_type=53", "author": {"author_id": "FABtotum", "display_name": "FABtotum S.R.L.", "email": "<EMAIL>", "website": "https://www.fabtotum.com"}}}, "FABtotumPLA": {"package_info": {"package_id": "FABtotumPLA", "package_type": "material", "display_name": "FABtotum PLA", "description": "It is the most common filament used for 3D printing. It is studied to be bio-degradable as it comes from corn starch’s sugar mainly. It is completely made of renewable sources and has no footprint on polluting. PLA stands for PolyLactic Acid and it is a thermoplastic that today is still considered the easiest material to be 3D printed. It can be extruded at lower temperatures: the standard range of FABtotum’s one is between 185° and 195°.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://store.fabtotum.com/eu/products/filaments.html?filament_type=39", "author": {"author_id": "FABtotum", "display_name": "FABtotum S.R.L.", "email": "<EMAIL>", "website": "https://www.fabtotum.com"}}}, "FABtotumTPU": {"package_info": {"package_id": "FABtotumTPU", "package_type": "material", "display_name": "FABtotum TPU Shore 98A", "description": "", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://store.fabtotum.com/eu/products/filaments.html?filament_type=66", "author": {"author_id": "FABtotum", "display_name": "FABtotum S.R.L.", "email": "<EMAIL>", "website": "https://www.fabtotum.com"}}}, "FiberlogyHDPLA": {"package_info": {"package_id": "FiberlogyHDPLA", "package_type": "material", "display_name": "Fiberlogy HD PLA", "description": "With our HD PLA you have many more options. You can use this material in two ways. Choose the one you like best. You can use it as a normal PLA and get prints characterized by a very good adhesion between the layers and high precision. You can also make your prints acquire similar properties to that of ABS – better impact resistance and high temperature resistance. All you need is an oven. Yes, an oven! By annealing our HD PLA in an oven, in accordance with the manual, you will avoid all the inconveniences of printing with ABS, such as unpleasant odour or hazardous fumes.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://fiberlogy.com/en/fiberlogy-filaments/filament-hd-pla/", "author": {"author_id": "Fiberlogy", "display_name": "Fiberlogy S.A.", "email": "<EMAIL>", "website": "http://fiberlogy.com"}}}, "Filo3DPLA": {"package_info": {"package_id": "Filo3DPLA", "package_type": "material", "display_name": "Filo3D PLA", "description": "Fast, safe and reliable printing. PLA is ideal for the fast and reliable printing of parts and prototypes with a great surface quality.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://dagoma.fr", "author": {"author_id": "Dagoma", "display_name": "Dagoma", "email": null, "website": "https://dagoma.fr"}}}, "IMADE3DJellyBOXPETG": {"package_info": {"package_id": "IMADE3DJellyBOXPETG", "package_type": "material", "display_name": "IMADE3D JellyBOX PETG", "description": "", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://shop.imade3d.com/filament.html", "author": {"author_id": "IMADE3D", "display_name": "IMADE3D", "email": "<EMAIL>", "website": "https://www.imade3d.com"}}}, "IMADE3DJellyBOXPLA": {"package_info": {"package_id": "IMADE3DJellyBOXPLA", "package_type": "material", "display_name": "IMADE3D JellyBOX PLA", "description": "", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://shop.imade3d.com/filament.html", "author": {"author_id": "IMADE3D", "display_name": "IMADE3D", "email": "<EMAIL>", "website": "https://www.imade3d.com"}}}, "JabilTPE_SEBS1300_95a": {"package_info": {"package_id": "JabilTPE_SEBS1300_95a", "package_type": "material", "display_name": "Jabil TPE SEBS 1300 95a", "description": "Soft material great for prototyping where rubber-like or elastomeric properties and durability are required.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://www.jabil.com/services/additive-manufacturing/additive-materials/compare-filaments/tpe-sebs-95a.html", "author": {"author_id": "<PERSON><PERSON><PERSON>", "display_name": "<PERSON><PERSON><PERSON>", "email": null, "website": "https://www.jabil.com/"}}}, "OctofiberPLA": {"package_info": {"package_id": "OctofiberPLA", "package_type": "material", "display_name": "Octofiber PLA", "description": "PLA material from Octofiber.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://nl.octofiber.com/3d-printing-filament/pla.html", "author": {"author_id": "Octofiber", "display_name": "Octofiber", "email": "<EMAIL>", "website": "https://nl.octofiber.com"}}}, "PolyFlexPLA": {"package_info": {"package_id": "PolyFlexPLA", "package_type": "material", "display_name": "PolyFlex™ PLA", "description": "PolyFlex™ is a highly flexible yet easy to print 3D printing material. Featuring good elasticity and a large strain-to- failure, PolyFlex™ opens up a completely new realm of applications.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://www.polymaker.com/shop/polyflex/", "author": {"author_id": "Polymaker", "display_name": "Polymaker L.L.C.", "email": "<EMAIL>", "website": "https://www.polymaker.com"}}}, "PolyMaxPC": {"package_info": {"package_id": "PolyMaxPC", "package_type": "material", "display_name": "PolyMax™ PC", "description": "PolyMax™ PC is an engineered PC filament combining excellent strength, toughness, heat resistance and printing quality. It is the ideal choice for a wide range of engineering applications.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://www.polymaker.com/shop/polymax/", "author": {"author_id": "Polymaker", "display_name": "Polymaker L.L.C.", "email": "<EMAIL>", "website": "https://www.polymaker.com"}}}, "PolyMaxPLA": {"package_info": {"package_id": "PolyMaxPLA", "package_type": "material", "display_name": "PolyMax™ PLA", "description": "PolyMax™ PLA is a 3D printing material with excellent mechanical properties and printing quality. PolyMax™ PLA has an impact resistance of up to nine times that of regular PLA, and better overall mechanical properties than ABS.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://www.polymaker.com/shop/polymax/", "author": {"author_id": "Polymaker", "display_name": "Polymaker L.L.C.", "email": "<EMAIL>", "website": "https://www.polymaker.com"}}}, "PolyPlusPLA": {"package_info": {"package_id": "PolyPlusPLA", "package_type": "material", "display_name": "PolyPlus™ PLA True Colour", "description": "PolyPlus™ PLA is a premium PLA designed for all desktop FDM/FFF 3D printers. It is produced with our patented Jam-Free™ technology that ensures consistent extrusion and prevents jams.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://www.polymaker.com/shop/polyplus-true-colour/", "author": {"author_id": "Polymaker", "display_name": "Polymaker L.L.C.", "email": "<EMAIL>", "website": "https://www.polymaker.com"}}}, "PolyWoodPLA": {"package_info": {"package_id": "PolyWoodPLA", "package_type": "material", "display_name": "PolyWood™ PLA", "description": "PolyWood™ is a wood mimic printing material that contains no actual wood ensuring a clean Jam-Free™ printing experience.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "http://www.polymaker.com/shop/polywood/", "author": {"author_id": "Polymaker", "display_name": "Polymaker L.L.C.", "email": "<EMAIL>", "website": "https://www.polymaker.com"}}}, "UltimakerABS": {"package_info": {"package_id": "UltimakerABS", "package_type": "material", "display_name": "Ultimaker ABS", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-abs/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-abs/printing-guidelines"}}}, "UltimakerBAM": {"package_info": {"package_id": "UltimakerBAM", "package_type": "material", "display_name": "Ultimaker Breakaway", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-breakaway/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-breakaway/printing-guidelines"}}}, "UltimakerCPE": {"package_info": {"package_id": "UltimakerCPE", "package_type": "material", "display_name": "Ultimaker CPE", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-cpe/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-cpe/printing-guidelines"}}}, "UltimakerCPEP": {"package_info": {"package_id": "UltimakerCPEP", "package_type": "material", "display_name": "Ultimaker CPE+", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-cpe-plus/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-cpe-plus/printing-guidelines"}}}, "UltimakerNylon": {"package_info": {"package_id": "UltimakerNylon", "package_type": "material", "display_name": "Ultimaker Nylon", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-nylon/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-nylon/printing-guidelines"}}}, "UltimakerPC": {"package_info": {"package_id": "UltimakerPC", "package_type": "material", "display_name": "Ultimaker PC", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-pc/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-pc/printing-guidelines"}}}, "UltimakerPETCF": {"package_info": {"package_id": "UltimakerPETCF", "package_type": "material", "display_name": "Ultimaker PETCF", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-pet-carbon-fiber/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-pet-cf/printing-guidelines"}}}, "UltimakerPETG": {"package_info": {"package_id": "UltimakerPETG", "package_type": "material", "display_name": "Ultimaker PETG", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-petg/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-petg/printing-guidelines"}}}, "UltimakerPLA": {"package_info": {"package_id": "UltimakerPLA", "package_type": "material", "display_name": "Ultimaker PLA", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-pla/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-pla/printing-guidelines"}}}, "UltimakerPP": {"package_info": {"package_id": "UltimakerPP", "package_type": "material", "display_name": "Ultimaker PP", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-pp/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-pp/printing-guidelines"}}}, "UltimakerPVA": {"package_info": {"package_id": "UltimakerPVA", "package_type": "material", "display_name": "Ultimaker PVA", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-pva/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-pva/printing-guidelines"}}}, "UltimakerTPLA": {"package_info": {"package_id": "UltimakerTPLA", "package_type": "material", "display_name": "<PERSON><PERSON><PERSON><PERSON>ugh PLA", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-tough-pla/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-tough-pla/printing-guidelines"}}}, "UltimakerTPU": {"package_info": {"package_id": "UltimakerTPU", "package_type": "material", "display_name": "Ultimaker TPU 95A", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/s-series-tpu-95a/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/in/cura/materials/ultimaker-tpu-95a/printing-guidelines"}}}, "UltimakerPPSCF": {"package_info": {"package_id": "UltimakerPPSCF", "package_type": "material", "display_name": "Ultimaker PPS-CF", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "1.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/factor-series-pps-carbon-fiber/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://support.ultimaker.com/s/article/How-to-print-with-UltiMaker-PPS-CF"}}}, "ULTIMAKERABSMETHOD": {"package_info": {"package_id": "ULTIMAKERABSMETHOD", "package_type": "material", "display_name": "ABS", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-abs/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "ULTIMAKERNYLONMETHOD": {"package_info": {"package_id": "ULTIMAKERNYLONMETHOD", "package_type": "material", "display_name": "Nylon", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-nylon/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "ULTIMAKERNYLONCFMETHOD": {"package_info": {"package_id": "ULTIMAKERNYLONCFMETHOD", "package_type": "material", "display_name": "Nylon Carbon Fiber", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-nylon-carbon-fiber/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "ULTIMAKERPLAMETHOD": {"package_info": {"package_id": "ULTIMAKERPLAMETHOD", "package_type": "material", "display_name": "PLA", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-pla/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "ULTIMAKERPVAMETHOD": {"package_info": {"package_id": "ULTIMAKERPVAMETHOD", "package_type": "material", "display_name": "PVA", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-pva/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "ULTIMAKERPETGMETHOD": {"package_info": {"package_id": "ULTIMAKERPETGMETHOD", "package_type": "material", "display_name": "PETG", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-petg/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "ULTIMAKERTOUGHMETHOD": {"package_info": {"package_id": "ULTIMAKERTOUGHMETHOD", "package_type": "material", "display_name": "Tough PLA", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-tough/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "BASFMETALMETHOD": {"package_info": {"package_id": "BASFMETALMETHOD", "package_type": "material", "display_name": "BASF Ultrafuse 316L", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-materials/#metal", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "JABILSEBSMETHOD": {"package_info": {"package_id": "JABILSEBSMETHOD", "package_type": "material", "display_name": "Jabil TPE SEBS 95A", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-materials/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "POLYMAKERPCMETHOD": {"package_info": {"package_id": "POLYMAKERPCMETHOD", "package_type": "material", "display_name": "Polymaker PolyMax PC", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-materials/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://ultimaker.com/materials/method-materials/"}}}, "ULTIMAKERBASCFMETHOD": {"package_info": {"package_id": "ULTIMAKERBASCFMETHOD", "package_type": "material", "display_name": "ABS-CF", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-abs-carbon-fiber/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://support.ultimaker.com/s/article/How-to-print-with-Method-ABS-CF"}}}, "ULTIMAKERABSRMETHOD": {"package_info": {"package_id": "ULTIMAKERABSRMETHOD", "package_type": "material", "display_name": "ABS-R", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-abs-r/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://support.ultimaker.com/s/article/How-to-print-with-Method-ABS-R"}}}, "ULTIMAKERASAMETHOD": {"package_info": {"package_id": "ULTIMAKERASAMETHOD", "package_type": "material", "display_name": "ASA", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-asa/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://support.ultimaker.com/s/article/How-to-print-with-Method-ASA"}}}, "ULTIMAKERNYLON12CFMETHOD": {"package_info": {"package_id": "ULTIMAKERNYLON12CFMETHOD", "package_type": "material", "display_name": "Nylon12 Carbon Fiber", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-nylon-12-carbon-fiber/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://support.ultimaker.com/s/article/How-to-print-with-Method-Nylon12-CF"}}}, "ULTIMAKERRAPIDRINSEMETHOD": {"package_info": {"package_id": "ULTIMAKERRAPIDRINSEMETHOD", "package_type": "material", "display_name": "RapidRinse", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-rapidrinse/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://support.ultimaker.com/s/article/How-to-print-with-Method-RapidRinse"}}}, "ULTIMAKERSR30METHOD": {"package_info": {"package_id": "ULTIMAKERSR30METHOD", "package_type": "material", "display_name": "SR-30", "description": "Example package for material and quality profiles for Ultimaker materials.", "package_version": "2.0.0", "sdk_version": "8.6.0", "website": "https://ultimaker.com/materials/method-series-sr-30/", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com", "description": "Professional 3D printing made accessible.", "support_website": "https://support.ultimaker.com/s/article/How-to-print-with-Method-SR-30"}}}, "VertexDeltaABS": {"package_info": {"package_id": "VertexDeltaABS", "package_type": "material", "display_name": "Vertex Delta ABS", "description": "ABS material and quality files for the Delta Vertex K8800.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://vertex3dprinter.eu", "author": {"author_id": "<PERSON><PERSON><PERSON>", "display_name": "Velleman N.V.", "email": "<EMAIL>", "website": "https://www.vellemanprojects.eu"}}}, "VertexDeltaPET": {"package_info": {"package_id": "VertexDeltaPET", "package_type": "material", "display_name": "Vertex Delta PET", "description": "ABS material and quality files for the Delta Vertex K8800.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://vertex3dprinter.eu", "author": {"author_id": "<PERSON><PERSON><PERSON>", "display_name": "Velleman N.V.", "email": "<EMAIL>", "website": "https://www.vellemanprojects.eu"}}}, "VertexDeltaPLA": {"package_info": {"package_id": "VertexDeltaPLA", "package_type": "material", "display_name": "Vertex Delta PLA", "description": "ABS material and quality files for the Delta Vertex K8800.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://vertex3dprinter.eu", "author": {"author_id": "<PERSON><PERSON><PERSON>", "display_name": "Velleman N.V.", "email": "<EMAIL>", "website": "https://www.vellemanprojects.eu"}}}, "VertexDeltaTPU": {"package_info": {"package_id": "VertexDeltaTPU", "package_type": "material", "display_name": "Vertex Delta TPU", "description": "ABS material and quality files for the Delta Vertex K8800.", "package_version": "1.4.0", "sdk_version": "8.6.0", "website": "https://vertex3dprinter.eu", "author": {"author_id": "<PERSON><PERSON><PERSON>", "display_name": "Velleman N.V.", "email": "<EMAIL>", "website": "https://www.vellemanprojects.eu"}}}}