name: unit-test

on:
  workflow_call:
    inputs:
      test_use_ctest:
        required: false
        default: false
        type: boolean

      test_use_pytest:
        required: false
        default: false
        type: boolean

      conan_extra_args:
        required: false
        default: ""
        type: string

      runs_on:
        required: false
        default: "ubuntu-latest"
        type: string


permissions:
  contents: read

jobs:
  testing:
    name: Run unit tests
    runs-on: ${{ inputs.runs_on }}

    steps:
      - name: Setup the build environment
        uses: ultimaker/cura-workflows/.github/actions/setup-build-environment@main
        with:
          install_system_dependencies: true

      - name: Install dependencies and build unit test
        run: conan build . -s build_type=Release --build=missing --update -c tools.build:skip_test=False ${{ inputs.test_use_pytest && '-g VirtualPythonEnv -c user.generator.virtual_python_env:dev_tools=True' || '' }} ${{ inputs.conan_extra_args }}

      - name: Run ctest-based unit test
        if: ${{ inputs.test_use_ctest }}
        run: |
          . ../generators/conanrun.sh
          ctest --output-junit unit_tests_results.xml
        working-directory: build/Release/tests

      - name: Run pytest-based unit test
        if: ${{ inputs.test_use_pytest }}
        run: |
          . ../build/generators/virtual_python_env.sh
          pytest --junitxml=unit_tests_results.xml
        working-directory: tests

      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: ./**/unit_tests_results.xml

      - name: Upload metadata
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-metadata
          path: ${{ github.event_path }}
