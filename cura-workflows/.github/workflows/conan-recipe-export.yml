name: Export Conan Recipe to server

on:
  workflow_call:
    inputs:
      repository:
        required: false
        default: ''
        type: string

      branch:
        required: false
        default: ''
        type: string

      version:
        required: true
        type: string

      user:
        required: false
        default: ''
        type: string

      channel:
        required: false
        default: ''
        type: string

      private_data:
        required: false
        default: false
        type: boolean

      conan_extra_args:
        required: false
        default: ""
        type: string

      conan_recipe_root:
        required: false
        default: "."
        type: string

permissions:
  contents: read

jobs:
  package-export:
    name: Upload package recipe
    runs-on: ubuntu-latest

    steps:
      - name: Setup the build environment
        uses: wsd07/cura-workflows/.github/actions/setup-build-environment@main
        with:
          repository: ${{ inputs.repository }}
          branch: ${{ inputs.branch }}
          conan_user: ${{ secrets.CONAN_USER }}
          conan_password: ${{ secrets.CONAN_PASS }}
          private_data: ${{ inputs.private_data }}

      - name: Export the Package to local cache
        id: export-local-cache
        run: |
          conan export ${{ inputs.conan_recipe_root }} --version ${{ inputs.version }} --user "${{ inputs.user }}" --channel "${{ inputs.channel }}" ${{ inputs.conan_extra_args }} -f json > export_output.json
          PACKAGE_REFERENCE=$(cat export_output.json | jq -r '.reference')
          echo "package_reference=$PACKAGE_REFERENCE" >> $GITHUB_OUTPUT

      - name: Upload the Recipe
        uses: wsd07/cura-workflows/.github/actions/upload-conan-package@main
        with:
          package_name: ${{ steps.export-local-cache.outputs.package_reference }}
