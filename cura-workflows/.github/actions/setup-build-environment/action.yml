# composite action, not to be run on its own, but included at the beginning of a build script

name: 'Setup the build environment'
description: 'Clones the required repositories, and properly sets Python and conan up'

inputs:
  repository:
    required: false
    default: ''
    type: string

  conan_user:
    required: false
    default: ''
    type: string

  conan_password:
    required: false
    default: ''
    type: string

  private_data:
    required: false
    default: false
    type: boolean

  install_system_dependencies:
    required: false
    default: false
    type: boolean

  branch:
    required: false
    default: ''
    type: string

  repository_path:
    required: false
    default: '.'
    type: string

  python_set_env_vars:
    required: false
    default: true
    type: boolean

  conan_config_branch:
    required: false
    default: 'master'
    type: string

outputs:
  package_name:
    value: ${{ steps.setup-environment.outputs.package_name }}
  package_version:
    value: ${{ steps.setup-environment.outputs.package_version }}

runs:
  using: "composite"
  steps:
    - name: Cleanup workspace
      shell: bash
      run: |
        set -e
        find . -mindepth 1 -delete

    - name: Checkout repo
      uses: actions/checkout@v4
      with:
        repository: ${{ inputs.repository }}
        ref: ${{ inputs.branch }}
        path: ${{ inputs.repository_path }}

    - name: Checkout Cura-workflows repo
      uses: actions/checkout@v4
      with:
        repository: Ultimaker/Cura-workflows
        path: Cura-workflows
        ref: main

    - name: Setup Python and pip
      uses: actions/setup-python@v5
      id: setup-python
      with:
        update-environment: ${{ inputs.python_set_env_vars }}
        python-version: '3.13'

    - name: Install Python requirements and setup Conan environment (Bash)
      id: setup-environment
      shell: bash
      run: |
        if [ "${{ inputs.install_system_dependencies }}" == "true" ]; then
          if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            chmod +x Cura-workflows/runner_scripts/ubuntu_setup.sh
            sudo ./Cura-workflows/runner_scripts/ubuntu_setup.sh
          elif [[ "$OSTYPE" == "darwin"* ]]; then
            chmod +x Cura-workflows/runner_scripts/macos_setup.sh
            ./Cura-workflows/runner_scripts/macos_setup.sh
          fi
        fi
        
        if [[ "${{ inputs.python_set_env_vars }}" == "false" ]]; then
          pydir="$(dirname "${{ steps.setup-python.outputs.python-path }}")"
          PATH+=":$pydir:$pydir/Scripts"
          echo $pydir >> pydir.txt
          echo "Installed Python for GitHub in: $pydir"
        fi

        python -m pip install -r Cura-workflows/.github/workflows/requirements-runner.txt

        if [ "${{ inputs.install_system_dependencies }}" == "true" ]; then
          if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            chmod +x Cura-workflows/runner_scripts/ubuntu_setup.sh
            sudo ./Cura-workflows/runner_scripts/ubuntu_setup.sh
          elif [[ "$OSTYPE" == "darwin"* ]]; then
            chmod +x Cura-workflows/runner_scripts/macos_setup.sh
            ./Cura-workflows/runner_scripts/macos_setup.sh
          fi
        fi

        conan profile detect -f
        conan config install https://github.com/Ultimaker/conan-config.git -a "-b ${{ inputs.conan_config_branch }}"

        if [ "${{ inputs.conan_user }}" != "" ] && [ "${{ inputs.conan_password }}" != "" ]; then
          conan remote login cura-conan2 ${{ inputs.conan_user }} -p ${{ inputs.conan_password }}
        fi

        if [ "${{ inputs.private_data }}" == "true" ]; then
          conan remote enable cura-private-conan2
          if [ "${{ inputs.conan_user }}" != "" ] && [ "${{ inputs.conan_password }}" != "" ]; then
            conan remote login cura-private-conan2 ${{ inputs.conan_user }} -p ${{ inputs.conan_password }}
          fi
        fi

        if [[ -f ${{ inputs.repository_path }}/conanfile.py ]]; then
          conan inspect ${{ inputs.repository_path }} > package_details.txt
          package_name=$(cat package_details.txt | awk '/^name:/ {print $2}')
          package_version=$(cat package_details.txt | awk '/^version:/ {print $2}')
          echo "package_name=$package_name" >> $GITHUB_OUTPUT
          echo "package_version=$package_version" >> $GITHUB_OUTPUT
        fi
