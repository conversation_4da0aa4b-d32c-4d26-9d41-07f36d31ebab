name: conan-package

on:
  workflow_dispatch:
  # push:
  #   paths:
  #     - 'plugins/**'
  #     - 'resources/**'
  #     - 'UM/**'
  #     - 'conanfile.py'
  #     - 'conandata.yml'
  #     - '.github/workflows/conan-package.yml'
  #   branches:
  #     - main
  #     - 'CURA-*'
  #     - 'PP-*'
  #     - 'NP-*'
  #     - '[0-9].[0-9]*'
  #     - '[0-9].[0-9][0-9]*'

jobs:
  conan-package:
    uses: wsd07/cura-workflows/.github/workflows/conan-package.yml@main
    with:
      platform_windows: false
      platform_mac: false
    secrets: inherit
