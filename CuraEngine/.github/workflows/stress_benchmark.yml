name: Stress Benchmark
on:
  push:
    paths:
      - 'include/**'
      - 'src/**'
      - 'stress_benchmark/**'
      - '.github/workflows/stress_benchmark.yml'
    branches:
      - main

  pull_request:
    types: [ opened, reopened, synchronize ]
    paths:
      - 'include/**'
      - 'src/**'
      - 'stress_benchmark/**'
      - '.github/workflows/stress_benchmark.yml'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - 'NP-*'
      - '[0-9]+.[0-9]+'

permissions:
  contents: write
  deployments: write


jobs:
  check_actor:
    uses: wsd07/cura-workflows/.github/workflows/check-actor.yml@main
    secrets: inherit

  benchmark:
    uses: wsd07/cura-workflows/.github/workflows/benchmark.yml@main
    with:
      conan_extra_args: "-c tools.build:skip_test=False -o \"curaengine/*:enable_benchmarks=True\""
      benchmark_cmd: "stress_benchmark/stress_benchmark -o benchmark_result.json"
      name: "Stress Benchmark"
      output_file_path: "benchmark_result.json"
      data_dir: "dev/stress_bench"
      tool: "customSmallerIsBetter"
    secrets: inherit
