name: Unit tests - run

on:
  push:
    paths:
      - 'include/**'
      - 'src/**'
      - 'tests/**'
      - 'conanfile.py'
      - 'conandata.yml'
      - 'CMakeLists.txt'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/unit-test-post.yml'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - 'NP-*'
      - '[0-9]+.[0-9]+'

  pull_request:
    types: [ opened, reopened, synchronize ]
    paths:
      - 'include/**'
      - 'src/**'
      - 'tests/**'
      - 'conanfile.py'
      - 'conandata.yml'
      - 'CMakeLists.txt'
      - '.github/workflows/unit-test.yml'
      - '.github/workflows/unit-test-post.yml'
    branches:
      - main
      - '[0-9]+.[0-9]+'

permissions:
  contents: read

jobs:
  testing:
    name: Run unit tests
    uses: wsd07/cura-workflows/.github/workflows/unit-test.yml@main
    with:
      test_use_ctest: true
