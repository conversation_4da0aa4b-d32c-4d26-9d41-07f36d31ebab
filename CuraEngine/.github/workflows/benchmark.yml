name: Benchmark

on:
  push:
    paths:
      - 'include/**'
      - 'src/**'
      - 'benchmark/**'
      - '.github/workflows/benchmark.yml'
    branches:
      - main

  pull_request:
    types: [ opened, reopened, synchronize ]
    paths:
      - 'include/**'
      - 'src/**'
      - 'benchmark/**'
      - '.github/workflows/benchmark.yml'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - 'NP-*'
      - '[0-9]+.[0-9]+'

permissions:
  contents: write
  deployments: write


jobs:
  check_actor:
    uses: wsd07/cura-workflows/.github/workflows/check-actor.yml@main
    secrets: inherit

  benchmark:
    uses: wsd07/cura-workflows/.github/workflows/benchmark.yml@main
    with:
      conan_extra_args: "-c tools.build:skip_test=False -o \"curaengine/*:enable_benchmarks=True\""
      benchmark_cmd: "benchmark/benchmarks --benchmark_format=json --benchmark_out=benchmark_result.json"
      name: "C++ Benchmark"
      output_file_path: "benchmark_result.json"
      data_dir: "dev/bench"
      tool: "googlecpp"
      alert_threshold: "150%"
      alert_comment_cc_users: "@nallath, @jellespijker, @wawanbreton, @casperlamboo, @saumyaj3, @HellAholic"
    secrets: inherit
