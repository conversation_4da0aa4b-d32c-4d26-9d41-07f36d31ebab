# Cura Development Environment - Workflow Documentation

## 概述

本文档详细介绍了Cura开发环境中所有GitHub Actions工作流的用途、使用方法和注意事项。整个系统包含4个主要仓库：

- **Cura**: 主要的GUI应用程序
- **CuraEngine**: 切片引擎
- **Uranium**: 应用程序框架
- **cura-workflows**: 共享的工作流模板

## 🔧 已完成的修改

### 独立化改造
所有工作流已经从依赖Ultimaker官方仓库改为使用您的私有仓库：
- `ultimaker/cura-workflows` → `wsd07/cura-workflows`
- `@ultimaker/testing` → `@wsd07/testing`
- `https://github.com/Ultimaker/conan-config.git` → `https://github.com/wsd07/conan-config.git`
- npm scope: `@ultimaker` → `@wsd07`

## 📁 工作流分类

### 1. 核心构建工作流 (cura-workflows)

#### 🏗️ 安装包构建
- **cura-installer-windows.yml**: Windows安装包构建
- **cura-installer-linux.yml**: Linux AppImage构建  
- **cura-installer-macos.yml**: macOS DMG/PKG构建
- **cura-installers.yml**: 多平台安装包构建调度器

#### 📦 包管理
- **conan-package.yml**: Conan包构建和发布
- **conan-package-release.yml**: 发布版本的Conan包构建
- **conan-recipe-export.yml**: Conan配方导出
- **conan-recipe-version.yml**: Conan包版本管理
- **npm-package.yml**: NPM包发布（WASM版本）

#### 🔍 包发现和管理
- **find-package-by-ticket.yml**: 根据Jira票号查找包
- **cura-set-packages-versions.yml**: 设置包版本

#### 🧪 测试和质量保证
- **unit-test.yml**: 单元测试执行
- **unit-test-post.yml**: 测试结果处理
- **benchmark.yml**: 性能基准测试
- **lint-formatter.yml**: 代码格式检查
- **lint-tidier.yml**: 代码整洁度检查

#### 🔄 流程管理
- **process-pull-request.yml**: PR处理流程
- **update-translation.yml**: 翻译更新
- **check-actor.yml**: 用户权限检查
- **make-runners-list.yml**: 构建运行器列表

### 2. Cura主项目工作流

#### 🚀 发布和构建
- **windows.yml**: Windows构建触发器
- **linux.yml**: Linux构建触发器
- **macos.yml**: macOS构建触发器
- **installers.yml**: 安装包构建触发器
- **build-installers.yml**: 构建安装包
- **nightly.yml**: 夜间构建
- **nightly-stable.yml**: 稳定版夜间构建
- **nightly-testing.yml**: 测试版夜间构建
- **release.yml**: 正式发布

#### 🔄 开发流程
- **build-test.yml**: 构建测试
- **quick-test.yml**: 快速测试
- **unit-test.yml**: 单元测试
- **unit-test-post.yml**: 测试后处理
- **process-pull-request.yml**: PR处理

#### 📦 包和资源
- **conan-package.yml**: Conan包构建
- **conan-package-resources.yml**: 资源包构建
- **find-packages.yml**: 包查找

#### 🔧 维护和管理
- **update-translation.yml**: 翻译更新
- **printer-linter-format.yml**: 打印机配置格式检查
- **printer-linter-pr-diagnose.yml**: 打印机配置PR诊断
- **printer-linter-pr-post.yml**: 打印机配置PR后处理
- **slicing-error-check.yml**: 切片错误检查
- **security_badge.yml**: 安全徽章更新
- **stale.yml**: 过期issue/PR管理
- **no-response.yml**: 无响应issue处理

#### 🏷️ 发布流程
- **release-process_feature-freeze.yml**: 功能冻结流程
- **release-process_release-candidate.yml**: 候选版本流程

### 3. CuraEngine工作流

#### 🧪 测试和质量
- **unit-test.yml**: 单元测试
- **unit-test-post.yml**: 测试后处理
- **benchmark.yml**: 性能测试
- **stress_benchmark.yml**: 压力测试
- **gcodeanalyzer.yml**: G-code分析

#### 🔧 代码质量
- **lint-formatter.yml**: 代码格式检查
- **lint-tidier.yml**: 代码整洁度检查
- **lint-poster.yml**: 代码检查结果发布

#### 📦 包管理
- **package.yml**: 包构建

#### 🔄 流程管理
- **process-pull-request.yml**: PR处理
- **scorecard.yml**: 安全评分
- **security_badge.yml**: 安全徽章

### 4. Uranium工作流

#### 📦 包管理
- **conan-package.yml**: Conan包构建

#### 🧪 测试
- **unit-test.yml**: 单元测试
- **unit-test-post.yml**: 测试后处理

#### 🔄 流程管理
- **process-pull-request.yml**: PR处理
- **update-translation.yml**: 翻译更新
- **security_badge.yml**: 安全徽章

## 🚀 使用方法

### 手动触发构建

#### Windows安装包构建
```bash
# 在Cura仓库中触发
gh workflow run windows.yml \
  --field cura_conan_version="cura/5.11.0@wsd07/testing" \
  --field enterprise=false \
  --field staging=false
```

#### Linux安装包构建
```bash
gh workflow run linux.yml \
  --field cura_conan_version="cura/5.11.0@wsd07/testing"
```

#### macOS安装包构建
```bash
gh workflow run macos.yml \
  --field cura_conan_version="cura/5.11.0@wsd07/testing" \
  --field architecture="ARM64"
```

### 包版本覆盖
使用`package_overrides`参数指定特定包版本：
```bash
gh workflow run windows.yml \
  --field package_overrides="uranium/5.11.0@wsd07/testing curaengine/5.11.0@wsd07/testing"
```

### Conan参数
使用`conan_args`传递额外的Conan参数：
```bash
gh workflow run windows.yml \
  --field conan_args="--build=missing --update"
```

## ⚠️ 重要注意事项

### 1. 依赖关系
- 所有构建都依赖于您的私有Conan仓库
- 确保所有依赖包都已发布到`@wsd07/testing`频道
- CuraEngine和Uranium必须先构建并发布，Cura才能构建

### 2. 密钥和凭证
需要配置以下GitHub Secrets：
- `CONAN_USER`: Conan用户名
- `CONAN_PASS`: Conan密码
- `CURAENGINE_SENTRY_TOKEN`: Sentry令牌
- `WIN_TOKEN_CONTAINER`: Windows代码签名
- `MACOS_CERT_P12`: macOS证书
- `GPG_PRIVATE_KEY`: Linux签名密钥

### 3. 构建顺序
推荐的构建顺序：
1. 首先构建CuraEngine
2. 然后构建Uranium
3. 最后构建Cura
4. 构建安装包

### 4. 版本管理
- 使用语义化版本号
- 开发版本使用`@wsd07/testing`频道
- 发布版本使用`@wsd07/stable`频道

### 5. 故障排除
- 检查Conan缓存是否正确配置
- 确认所有依赖包版本兼容
- 查看构建日志中的详细错误信息
- 验证代码签名证书是否有效

## 🔄 自动化流程

### PR流程
1. 提交PR时自动触发测试
2. 代码格式和质量检查
3. 单元测试执行
4. 构建验证

### 夜间构建
- 每晚自动构建最新代码
- 生成测试版安装包
- 运行完整测试套件

### 发布流程
1. 创建发布分支
2. 功能冻结
3. 候选版本构建
4. 最终发布

## 📞 支持

如遇问题，请检查：
1. GitHub Actions日志
2. Conan包依赖关系
3. 密钥配置
4. 网络连接和权限

## 📋 详细工作流说明

### cura-workflows 核心工作流详解

#### cura-installer-windows.yml
**用途**: 构建Windows安装包（.exe和.msi）
**触发方式**: workflow_call
**输入参数**:
- `cura_conan_version`: Cura包版本
- `package_overrides`: 包覆盖列表
- `conan_args`: Conan额外参数
- `enterprise`: 企业版标志
- `staging`: 使用staging API
- `operating_system`: 操作系统选择
- `private_data`: 私有数据标志

**输出产物**:
- Windows .exe安装包
- Windows .msi安装包
- UltiMaker-Cura.exe应用程序
- CuraEngine.exe引擎

**特殊功能**:
- 代码签名（需要证书）
- MSVC运行时库处理
- Qt文件清理优化

#### cura-installer-linux.yml
**用途**: 构建Linux AppImage包
**触发方式**: workflow_call
**输入参数**: 与Windows版本类似
**输出产物**:
- .AppImage文件
- .asc签名文件

**特殊功能**:
- GPG签名
- AppImage打包
- 依赖库处理

#### cura-installer-macos.yml
**用途**: 构建macOS安装包（.dmg和.pkg）
**触发方式**: workflow_call
**输入参数**: 包含架构选择（ARM64/X64）
**输出产物**:
- .dmg磁盘镜像
- .pkg安装包

**特殊功能**:
- 代码签名和公证
- 多架构支持
- Keychain管理

#### conan-package.yml
**用途**: 构建和发布Conan包
**触发方式**: workflow_call
**功能**:
- 编译C++代码
- 运行测试
- 上传到Conan仓库

#### unit-test.yml
**用途**: 执行单元测试
**触发方式**: workflow_call
**功能**:
- Python测试执行
- 覆盖率报告
- 测试结果收集

### Cura主项目工作流详解

#### windows.yml / linux.yml / macos.yml
**用途**: 平台特定的构建触发器
**触发方式**: workflow_dispatch（手动触发）
**功能**: 调用cura-workflows中对应的安装包构建工作流

#### nightly.yml
**用途**: 夜间自动构建
**触发方式**: schedule（定时触发）
**功能**:
- 自动获取最新代码
- 构建所有平台安装包
- 发布到测试频道

#### release.yml
**用途**: 正式版本发布
**触发方式**: workflow_dispatch
**功能**:
- 版本标记
- 构建发布版安装包
- 发布到GitHub Releases

### 包管理和依赖

#### 包命名规范
- 开发版本: `package/version@wsd07/testing`
- 稳定版本: `package/version@wsd07/stable`
- 特性分支: `package/version@wsd07/feature_branch`

#### 依赖关系图
```
Cura
├── CuraEngine
├── Uranium
├── cura_binary_data
├── fdm_materials
└── cura_resources
```

#### 版本同步
所有核心包应使用相同的版本号以确保兼容性。

## 🛠️ 开发工作流程

### 1. 功能开发
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和测试
# ... 编码 ...

# 3. 提交PR
git push origin feature/new-feature
# 在GitHub上创建PR

# 4. 自动触发测试
# PR会自动触发单元测试和构建验证
```

### 2. 包发布流程
```bash
# 1. 更新版本号
# 编辑conandata.yml中的version

# 2. 构建包
gh workflow run conan-package.yml

# 3. 测试包
gh workflow run unit-test.yml

# 4. 发布到测试频道
# 包会自动发布到@wsd07/testing
```

### 3. 安装包构建
```bash
# 1. 确保所有依赖包已发布
# 2. 触发安装包构建
gh workflow run installers.yml \
  --field cura_conan_version="cura/5.11.0@wsd07/testing"

# 3. 下载构建产物
gh run download <run-id>
```

## 🔧 配置和定制

### 环境变量配置
在GitHub仓库设置中配置以下环境变量：

#### Conan配置
- `CONAN_USER`: 您的Conan用户名
- `CONAN_PASS`: Conan密码或API密钥

#### 代码签名（可选）
- `WIN_TOKEN_CONTAINER`: Windows代码签名容器
- `MACOS_CERT_P12`: macOS开发者证书
- `MACOS_CERT_PASSPHRASE`: 证书密码
- `GPG_PRIVATE_KEY`: Linux GPG私钥

#### 外部服务
- `CURAENGINE_SENTRY_TOKEN`: Sentry错误追踪
- `GITHUB_TOKEN`: 自动配置，用于包发布

### 自定义构建配置
可以通过修改以下文件来定制构建：
- `conan-config/profiles/`: Conan构建配置
- `cura-workflows/runner_scripts/`: 构建脚本
- `.github/workflows/`: 工作流定义

## 🚨 故障排除指南

### 常见问题

#### 1. Conan包找不到
**症状**: `ERROR: Package not found`
**解决方案**:
- 检查包名和版本是否正确
- 确认包已发布到正确的频道
- 验证Conan远程仓库配置

#### 2. 构建失败
**症状**: 编译错误或链接错误
**解决方案**:
- 检查依赖包版本兼容性
- 查看详细构建日志
- 验证编译器版本和设置

#### 3. 代码签名失败
**症状**: 签名步骤失败
**解决方案**:
- 检查证书是否有效
- 验证密钥配置
- 确认签名权限

#### 4. 测试失败
**症状**: 单元测试不通过
**解决方案**:
- 本地运行测试
- 检查测试环境差异
- 更新测试数据

### 调试技巧

#### 1. 启用详细日志
在workflow中添加：
```yaml
env:
  CONAN_LOG_LEVEL: debug
  PYTHONPATH: .
```

#### 2. 保留构建产物
```yaml
- name: Upload debug artifacts
  if: failure()
  uses: actions/upload-artifact@v4
  with:
    name: debug-logs
    path: |
      *.log
      conan_cache/
```

#### 3. SSH调试（仅用于调试）
```yaml
- name: Setup tmate session
  if: failure()
  uses: mxschmitt/action-tmate@v3
```

---
*文档最后更新: 2025-06-29*
